#!/bin/bash
#===========================================
# Simple Battery Management - Fixed Version
# Focuses on core charging logic only
#===========================================

# Configuration
WEBHOOK_KEY="990B939B49"
LOWER_THRESHOLD=70
UPPER_THRESHOLD=90
CONFIG_DIR="$HOME/.config/battery-health"
LOG_FILE="$CONFIG_DIR/battery-simple.log"

# Create config directory
mkdir -p "$CONFIG_DIR"

# Logging function
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Get battery percentage
get_battery_percentage() {
    local battery_info
    battery_info=$(pmset -g batt 2>/dev/null)
    log_message "DEBUG" "Raw battery info: $battery_info"
    
    if [[ "$battery_info" =~ ([0-9]+)% ]]; then
        local percentage="${BASH_REMATCH[1]}"
        log_message "DEBUG" "Battery percentage: ${percentage}%"
        echo "$percentage"
    else
        log_message "WARN" "Could not parse battery percentage"
        echo "50"
    fi
}

# Get charging state
get_charging_state() {
    local battery_info
    battery_info=$(pmset -g batt 2>/dev/null)
    log_message "DEBUG" "Checking charging state from: $battery_info"
    
    if echo "$battery_info" | grep -q "AC Power"; then
        log_message "DEBUG" "On AC Power"
        if echo "$battery_info" | grep -q "charging"; then
            echo "charging"
        elif echo "$battery_info" | grep -q "charged"; then
            echo "charged"
        else
            echo "plugged_not_charging"
        fi
    elif echo "$battery_info" | grep -q "Battery Power"; then
        log_message "DEBUG" "On Battery Power"
        echo "discharging"
    else
        log_message "WARN" "Unknown power state"
        echo "unknown"
    fi
}

# Get power source
get_power_source() {
    local battery_info
    battery_info=$(pmset -g batt 2>/dev/null)
    
    if echo "$battery_info" | grep -q "AC Power"; then
        echo "ac"
    elif echo "$battery_info" | grep -q "Battery Power"; then
        echo "battery"
    else
        echo "unknown"
    fi
}

# Send webhook
trigger_webhook() {
    local action="$1"
    local reason="${2:-battery_management}"
    
    log_message "INFO" "Triggering webhook: $action (reason: $reason)"
    
    local webhook_id
    local url
    
    if [[ "$action" == "on" ]]; then
        webhook_id="66748"
        url="https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/$webhook_id/switch_on"
    elif [[ "$action" == "off" ]]; then
        webhook_id="66750"
        url="https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/$webhook_id/switch_off"
    else
        log_message "ERROR" "Invalid webhook action: $action"
        return 1
    fi
    
    log_message "DEBUG" "Webhook URL: $url"
    
    # Try webhook with timeout
    if curl -s -m 10 -X POST "$url" >/dev/null 2>&1; then
        log_message "INFO" "Webhook $action successful"
        return 0
    else
        log_message "ERROR" "Webhook $action failed"
        return 1
    fi
}

# Main battery management
manage_battery() {
    log_message "INFO" "=== Starting Simple Battery Management ==="
    
    # Get current status
    local battery_perc
    battery_perc=$(get_battery_percentage)
    
    local charging_state
    charging_state=$(get_charging_state)
    
    local power_source
    power_source=$(get_power_source)
    
    log_message "INFO" "Current Status - Battery: ${battery_perc}%, Charging: $charging_state, Power: $power_source"
    log_message "INFO" "Thresholds - Lower: ${LOWER_THRESHOLD}%, Upper: ${UPPER_THRESHOLD}%"
    
    # Simple charging logic
    if [[ "$power_source" == "ac" ]]; then
        log_message "INFO" "On AC power - can manage charging"
        
        if [[ "$battery_perc" -le $LOWER_THRESHOLD ]]; then
            log_message "INFO" "Battery ${battery_perc}% <= ${LOWER_THRESHOLD}% - should start charging"
            if [[ "$charging_state" != "charging" ]]; then
                log_message "INFO" "Starting charge (current state: $charging_state)"
                if trigger_webhook "on" "battery_low"; then
                    log_message "INFO" "✅ Charging started successfully"
                else
                    log_message "ERROR" "❌ Failed to start charging"
                fi
            else
                log_message "INFO" "Already charging - no action needed"
            fi
        elif [[ "$battery_perc" -ge $UPPER_THRESHOLD ]]; then
            log_message "INFO" "Battery ${battery_perc}% >= ${UPPER_THRESHOLD}% - should stop charging"
            if [[ "$charging_state" == "charging" ]]; then
                log_message "INFO" "Stopping charge"
                if trigger_webhook "off" "battery_high"; then
                    log_message "INFO" "✅ Charging stopped successfully"
                else
                    log_message "ERROR" "❌ Failed to stop charging"
                fi
            else
                log_message "INFO" "Not charging - no action needed"
            fi
        else
            log_message "INFO" "Battery in normal range (${LOWER_THRESHOLD}%-${UPPER_THRESHOLD}%) - no action needed"
        fi
    else
        log_message "INFO" "Not on AC power - cannot manage charging"
    fi
    
    log_message "INFO" "=== Battery Management Completed ==="
}

# Show status
show_status() {
    echo "🔋 Simple Battery Management System"
    echo "==================================="
    
    local battery_perc=$(get_battery_percentage)
    local charging_state=$(get_charging_state)
    local power_source=$(get_power_source)
    
    echo "Battery Level: ${battery_perc}%"
    echo "Charging State: $charging_state"
    echo "Power Source: $power_source"
    echo "Lower Threshold: ${LOWER_THRESHOLD}%"
    echo "Upper Threshold: ${UPPER_THRESHOLD}%"
    echo "Log File: $LOG_FILE"
    
    # Decision logic
    echo ""
    echo "Decision Logic:"
    if [[ "$power_source" == "ac" ]]; then
        if [[ "$battery_perc" -le $LOWER_THRESHOLD ]]; then
            echo "🔥 ACTION: Should start charging (battery low)"
        elif [[ "$battery_perc" -ge $UPPER_THRESHOLD ]]; then
            echo "🛑 ACTION: Should stop charging (battery high)"
        else
            echo "✅ STATUS: Battery in normal range - no action needed"
        fi
    else
        echo "ℹ️  STATUS: Not on AC power - cannot manage charging"
    fi
}

# Main execution
case "${1:-manage}" in
"manage")
    manage_battery
    ;;
"status")
    show_status
    ;;
"help")
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  manage  - Run battery management (default)"
    echo "  status  - Show current status"
    echo "  help    - Show this help"
    ;;
*)
    echo "Unknown command: $1"
    echo "Use '$0 help' for usage information."
    exit 1
    ;;
esac
