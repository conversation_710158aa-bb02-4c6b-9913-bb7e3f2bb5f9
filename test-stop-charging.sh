#!/bin/bash

echo "🔋 Testing Stop Charging Logic at 96%"
echo "====================================="

# Test current battery state
echo "1. Current Battery Status:"
battery_info=$(pmset -g batt 2>/dev/null)
echo "Raw pmset output: $battery_info"

# Parse battery percentage
if [[ "$battery_info" =~ ([0-9]+)% ]]; then
    battery_perc="${BASH_REMATCH[1]}"
    echo "✅ Battery percentage: ${battery_perc}%"
else
    echo "❌ Could not parse battery percentage"
    battery_perc=96  # Assume 96% for testing
fi

# Parse charging state
if echo "$battery_info" | grep -q "AC Power"; then
    echo "✅ On AC Power"
    if echo "$battery_info" | grep -q "charging"; then
        charging_state="charging"
        echo "⚡ Currently charging"
    elif echo "$battery_info" | grep -q "charged"; then
        charging_state="charged"
        echo "✅ Fully charged"
    else
        charging_state="plugged_not_charging"
        echo "🔌 Plugged in but not charging"
    fi
elif echo "$battery_info" | grep -q "Battery Power"; then
    charging_state="discharging"
    echo "🔋 On Battery Power"
else
    charging_state="unknown"
    echo "❓ Unknown power state"
fi

echo ""
echo "2. Stop Charging Logic Test:"
UPPER_THRESHOLD=90
echo "Battery: ${battery_perc}%"
echo "Upper threshold: ${UPPER_THRESHOLD}%"
echo "Current charging state: $charging_state"

# Test the logic
if [[ "$battery_perc" -ge $UPPER_THRESHOLD ]]; then
    echo "🛑 Battery ${battery_perc}% >= ${UPPER_THRESHOLD}% - SHOULD STOP CHARGING"
    
    # Check if any charging-related state
    if [[ "$charging_state" == "charging" ]] || [[ "$charging_state" == "charged" ]] || [[ "$charging_state" == "plugged_not_charging" ]] || [[ "$charging_state" == "unknown" ]]; then
        echo "✅ Charging state ($charging_state) indicates stop action needed"
        echo "🔥 ACTION: Would send webhook to STOP charging"
        
        # Test webhook URL
        WEBHOOK_KEY="990B939B49"
        webhook_url="https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66750/switch_off"
        echo "Webhook URL: $webhook_url"
        
        # Test connectivity
        echo ""
        echo "3. Testing webhook connectivity:"
        if curl -s -m 10 -X POST "$webhook_url" >/dev/null 2>&1; then
            echo "✅ Webhook call successful"
        else
            echo "❌ Webhook call failed"
        fi
    else
        echo "ℹ️  No stop action needed (state: $charging_state)"
    fi
else
    echo "ℹ️  Battery below threshold - no stop action needed"
fi

echo ""
echo "4. Running actual battery management:"
echo "======================================"
cd /Users/<USER>/.scripts
./battery-limit.sh manage

echo ""
echo "5. Checking recent logs:"
echo "========================"
if [[ -f "$HOME/.config/battery-health/battery.log" ]]; then
    echo "Last 10 log entries:"
    tail -10 "$HOME/.config/battery-health/battery.log"
else
    echo "Log file not found"
fi
