#!/bin/bash

echo "🔧 Setting up Battery Management Crontab (Fixed Version)"
echo "========================================================"

# Get absolute paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPT_PATH="$SCRIPT_DIR/battery-limit.sh"
USER_HOME="$(eval echo ~$USER)"

echo "Script directory: $SCRIPT_DIR"
echo "Script path: $SCRIPT_PATH"
echo "User home: $USER_HOME"

# Verify script exists and is executable
if [[ ! -f "$SCRIPT_PATH" ]]; then
    echo "❌ Error: Script not found at $SCRIPT_PATH"
    exit 1
fi

# Make script executable
chmod +x "$SCRIPT_PATH"
echo "✅ Script permissions set"

# Create log directory
mkdir -p "$USER_HOME/.battery_health_manager"
echo "✅ Log directory created"

# Remove any existing battery-limit entries
echo "Removing existing battery-limit crontab entries..."
crontab -l 2>/dev/null | grep -v "battery-limit.sh" | crontab -

# Create crontab entry with full paths and error logging
echo "Adding new crontab entry with full paths and error logging..."
CRONTAB_ENTRY="* * * * * CRON_EXECUTION=1 /usr/bin/bash $SCRIPT_PATH manage --debug-env 2>>$USER_HOME/.battery_health_manager/cron_errors.log"

echo "Crontab entry: $CRONTAB_ENTRY"

(crontab -l 2>/dev/null; echo "$CRONTAB_ENTRY") | crontab -

echo ""
echo "✅ Crontab updated. Current crontab entries:"
crontab -l

echo ""
echo "🧪 Testing the script manually first..."
echo "======================================="

cd "$SCRIPT_DIR"

# Test the script manually with debug environment
echo "Running: $SCRIPT_PATH manage --debug-env"
"$SCRIPT_PATH" manage --debug-env

echo ""
echo "📋 Checking logs:"
echo "================="

if [[ -f "$USER_HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "Recent log entries:"
    tail -30 "$USER_HOME/.battery_health_manager/battery_health.log"
else
    echo "❌ No log file found at $USER_HOME/.battery_health_manager/battery_health.log"
fi

echo ""
echo "🔍 Checking for cron errors:"
echo "============================"

if [[ -f "$USER_HOME/.battery_health_manager/cron_errors.log" ]]; then
    echo "Cron error log contents:"
    cat "$USER_HOME/.battery_health_manager/cron_errors.log"
else
    echo "✅ No cron error log (this is good)"
fi

echo ""
echo "📊 Current battery status:"
echo "=========================="
pmset -g batt

echo ""
echo "🎯 Next Steps:"
echo "=============="
echo "1. Wait 1-2 minutes for cron to run"
echo "2. Check logs: tail -f ~/.battery_health_manager/battery_health.log"
echo "3. Check cron errors: cat ~/.battery_health_manager/cron_errors.log"
echo "4. Verify webhook execution in the logs"

echo ""
echo "🔧 Troubleshooting Commands:"
echo "============================"
echo "View crontab: crontab -l"
echo "Remove crontab: crontab -r"
echo "Check logs: tail -f ~/.battery_health_manager/battery_health.log"
echo "Manual test: $SCRIPT_PATH manage --debug-env"
