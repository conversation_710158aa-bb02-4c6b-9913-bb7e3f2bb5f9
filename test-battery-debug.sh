#!/bin/bash

echo "Testing battery script with debug output..."

# Run the battery script and capture output
echo "Running: ./battery-limit.sh manage"
echo "=================================="

# Set environment variable for webhook key
export WEBHOOK_KEY="990B939B49"

# Run with timeout to prevent hanging
timeout 30 bash battery-limit.sh manage 2>&1

echo ""
echo "Script execution completed."

# Check the log file for debug information
echo ""
echo "Recent log entries:"
echo "==================="
if [[ -f "$HOME/.config/battery-health/battery.log" ]]; then
    tail -20 "$HOME/.config/battery-health/battery.log"
else
    echo "Log file not found"
fi
