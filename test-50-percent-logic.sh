#!/bin/bash

echo "🔍 Testing 50% Battery Logic"
echo "============================"

# Test the exact logic with 50% battery
battery_perc=50
effective_lower_threshold=70
effective_upper_threshold=90

echo "Test Variables:"
echo "  battery_perc=$battery_perc"
echo "  effective_lower_threshold=$effective_lower_threshold"
echo "  effective_upper_threshold=$effective_upper_threshold"

echo ""
echo "🧪 Testing Logic Flow:"

should_charge=false
should_stop_charge=false
charge_reason=""

# Test upper threshold first (should be false at 50%)
echo "1. Testing upper threshold: [[ $battery_perc -ge $effective_upper_threshold ]]"
if [[ "$battery_perc" -ge $effective_upper_threshold ]]; then
    echo "   ✅ TRUE: $battery_perc >= $effective_upper_threshold - STOP CHARGING"
    should_stop_charge=true
    charge_reason="battery_high"
else
    echo "   ❌ FALSE: $battery_perc < $effective_upper_threshold - checking lower threshold"
    
    # Test lower threshold (should be true at 50%)
    echo "2. Testing lower threshold: [[ $battery_perc -le $effective_lower_threshold ]]"
    if [[ "$battery_perc" -le $effective_lower_threshold ]]; then
        echo "   ✅ TRUE: $battery_perc <= $effective_lower_threshold - START CHARGING"
        should_charge=true
        charge_reason="battery_low"
    else
        echo "   ❌ FALSE: $battery_perc > $effective_lower_threshold - no action"
    fi
fi

echo ""
echo "🎯 Final Decision Variables:"
echo "  should_charge=$should_charge"
echo "  should_stop_charge=$should_stop_charge"
echo "  charge_reason=$charge_reason"

echo ""
echo "📋 Expected vs Actual:"
if [[ "$should_charge" == true ]] && [[ "$should_stop_charge" == false ]]; then
    echo "✅ CORRECT: At 50%, should START charging (send 'on' webhook)"
elif [[ "$should_stop_charge" == true ]] && [[ "$should_charge" == false ]]; then
    echo "❌ ERROR: At 50%, incorrectly wants to STOP charging (send 'off' webhook)"
else
    echo "❌ ERROR: Unexpected state - both or neither flags set"
fi

echo ""
echo "🚀 Now testing actual script execution:"
echo "======================================="

# Create config directory
mkdir -p "$HOME/.battery_health_manager"

# Run the actual script
cd /Users/<USER>/.scripts
echo "Running: ./battery-limit.sh manage"
./battery-limit.sh manage

echo ""
echo "📋 Checking logs for webhook calls:"
if [[ -f "$HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "Recent webhook-related log entries:"
    grep -i "webhook\|executing\|trigger" "$HOME/.battery_health_manager/battery_health.log" | tail -10
else
    echo "❌ No log file found"
fi
