{"version": "2.0", "description": "Advanced Battery Health Manager Configuration", "charging_thresholds": {"lower_threshold": 20, "upper_threshold": 80, "travel_threshold": 100, "sailing_threshold": 40}, "thermal_protection": {"temp_warning": 45, "temp_critical": 55, "thermal_monitoring_enabled": true}, "scheduling": {"schedule_enabled": true, "night_hours_start": 22, "night_hours_end": 6, "work_days_only": true, "predictive_charging_enabled": true}, "health_management": {"sailing_mode_enabled": true, "sailing_interval_days": 7, "health_monitoring_enabled": true, "cycle_count_warning": 500, "capacity_warning": 90}, "notifications": {"enabled": true, "thermal_warnings": true, "health_warnings": true, "mode_changes": true, "charging_events": true}, "external_monitor": {"clamshell_mode_enabled": true, "sleep_protection_enabled": true, "auto_detect_enabled": true}, "webhook": {"url_on": "https://maker.ifttt.com/trigger/battery_charge_on/with/key/YOUR_KEY", "url_off": "https://maker.ifttt.com/trigger/battery_charge_off/with/key/YOUR_KEY", "retry_attempts": 3, "timeout": 10}, "logging": {"log_level": "INFO", "max_log_size_mb": 10, "health_metrics_enabled": true, "usage_tracking_enabled": true}}