#!/bin/bash
#===========================================
# Smart Battery Management Script for macOS
# Enhanced Version
#===========================================
# Configuration
LOWER_THRESHOLD=70
UPPER_THRESHOLD=90
WEBHOOK_KEY="990B939B49"
LOG_FILE="/tmp/battery_management.log"
STATE_FILE="/tmp/battery_state.txt"

# Get battery percentage
get_battery_percentage() {
    battery_info=$(pmset -g batt)
    if [[ "$battery_info" =~ ([0-9]+)% ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo "50" # Default value if unable to get battery percentage
    fi
}

# Trigger webhook function
trigger_webhook() {
    local action=$1
    if [[ "$action" == "on" ]]; then
        curl -s -X POST "https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66748/switch_on" >/dev/null 2>&1
        elif [[ "$action" == "off" ]]; then
        curl -s -X POST "https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66750/switch_off" >/dev/null 2>&1
    fi
}

# Log message function
log_message() {
    local message=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Send notification function
send_notification() {
    local message=$1
    osascript -e "display notification \"$message\" with title \"Battery Management\""
}

# Start sleep protection
start_sleep_protection() {
    sudo pmset -a sleep 0
    sudo pmset -a hibernatemode 0
    sudo pmset -a disablesleep 1
    # Verify sleep protection status
    sleep_status=$(pmset -g custom | awk '/^ ?sleep/ {print $2}')
    log_message "Sleep protection enabled. Sleep status: $sleep_status"
    send_notification "Sleep protection enabled. Sleep status: $sleep_status"
}

# Stop sleep protection
stop_sleep_protection() {
    sudo pmset -a sleep 1
    sudo pmset -a hibernatemode 3
    sudo pmset -a disablesleep 0
    # Verify sleep protection status
    sleep_status=$(pmset -g custom | awk '/^ ?sleep/ {print $2}')
    log_message "Sleep protection disabled. Sleep status: $sleep_status"
    send_notification "Sleep protection disabled. Sleep status: $sleep_status"
}

# Check if external monitor is connected and used as main display
is_external_monitor_connected() {
    display_info=$(system_profiler SPDisplaysDataType)
    
    # Check for external display (EB321HQU or any display with QHD resolution)
    if echo "$display_info" | grep -q "2560 x 1440.*QHD"; then
        # External QHD monitor is connected
        return 0
    fi
    
    # If no external QHD monitor, check if only internal display is present
    internal_count=$(echo "$display_info" | grep -c "Connection Type: Internal")
    total_displays=$(echo "$display_info" | grep -c "Resolution:")
    
    if [ "$internal_count" -eq 1 ] && [ "$total_displays" -eq 1 ]; then
        # Only internal display is connected
        return 1
        elif [ "$internal_count" -eq 0 ] && [ "$total_displays" -ge 1 ]; then
        # Internal display is closed (lid closed) and external monitor is connected
        return 0
    else
        # Default case - assume no external monitor
        return 1
    fi
}

# Read states from STATE_FILE
read_states() {
    monitor_state="disconnected"
    charging_state="off"
    battery_state="normal"
    
    if [ -f "$STATE_FILE" ]; then
        while IFS='=' read -r key value; do
            case "$key" in
                monitor) monitor_state="$value" ;;
                charging) charging_state="$value" ;;
                battery) battery_state="$value" ;;
            esac
        done < "$STATE_FILE"
    fi
}

# Write states to STATE_FILE
write_states() {
    {
        echo "monitor=$monitor_state"
        echo "charging=$charging_state"
        echo "battery=$battery_state"
    } > "$STATE_FILE"
}

# Main function
manage_battery() {
    battery_perc=$(get_battery_percentage)
    read_states
    
    # Check external monitor connection
    if is_external_monitor_connected; then
        if [ "$monitor_state" != "connected" ]; then
            start_sleep_protection
            log_message "External monitor connected. Sleep protection enabled."
            send_notification "External monitor connected. Sleep protection enabled."
            monitor_state="connected"
        fi
    else
        if [ "$monitor_state" != "disconnected" ]; then
            stop_sleep_protection
            log_message "External monitor disconnected. Sleep protection disabled."
            send_notification "External monitor disconnected. Sleep protection disabled."
            monitor_state="disconnected"
        fi
    fi
    
    # Manage charging based on battery levels
    if [ "$battery_perc" -le "$LOWER_THRESHOLD" ]; then
        if [ "$charging_state" != "off" ]; then
            trigger_webhook "on"
            send_notification "Battery low (${battery_perc}%). Charging started."
            log_message "Charging started at ${battery_perc}%."
            charging_state="on"
        fi
        elif [ "$battery_perc" -ge "$UPPER_THRESHOLD" ]; then
        if [ "$charging_state" != "on" ]; then
            trigger_webhook "off"
            send_notification "Battery high (${battery_perc}%). Charging stopped."
            log_message "Charging stopped at ${battery_perc}%."
            charging_state="off"
        fi
    fi
    
    # Save current states to STATE_FILE
    write_states
}

manage_battery
