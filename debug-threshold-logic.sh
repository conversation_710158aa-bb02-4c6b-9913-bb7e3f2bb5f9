#!/bin/bash

echo "🔍 Debug Threshold Logic"
echo "========================"

# Test the exact logic from the script
battery_perc=96
effective_lower_threshold=70
effective_upper_threshold=90

echo "Variables:"
echo "  battery_perc=$battery_perc"
echo "  effective_lower_threshold=$effective_lower_threshold"
echo "  effective_upper_threshold=$effective_upper_threshold"

echo ""
echo "Testing conditions:"

# Test upper threshold first (this should be true)
echo "1. Testing: [[ $battery_perc -ge $effective_upper_threshold ]]"
if [[ "$battery_perc" -ge $effective_upper_threshold ]]; then
    echo "   ✅ TRUE: $battery_perc >= $effective_upper_threshold - SHOULD STOP CHARGING"
    action="STOP"
else
    echo "   ❌ FALSE: $battery_perc < $effective_upper_threshold"
    
    # Test lower threshold
    echo "2. Testing: [[ $battery_perc -le $effective_lower_threshold ]]"
    if [[ "$battery_perc" -le $effective_lower_threshold ]]; then
        echo "   ✅ TRUE: $battery_perc <= $effective_lower_threshold - SHOULD START CHARGING"
        action="START"
    else
        echo "   ❌ FALSE: $battery_perc > $effective_lower_threshold - NO ACTION"
        action="NONE"
    fi
fi

echo ""
echo "🎯 RESULT: Action should be: $action"

if [[ "$action" == "STOP" ]]; then
    echo "✅ CORRECT: At 96%, script should STOP charging"
else
    echo "❌ ERROR: At 96%, script should STOP charging, but logic says: $action"
fi

echo ""
echo "🔍 Let's also test the exact script logic structure:"

# Simulate the exact if-elif structure from the script
should_charge=false
should_stop_charge=false
charge_reason=""

if [[ "$battery_perc" -ge $effective_upper_threshold ]]; then
    echo "✅ Upper threshold condition met"
    should_stop_charge=true
    charge_reason="battery_high"
    echo "   should_stop_charge=$should_stop_charge"
    echo "   charge_reason=$charge_reason"
elif [[ "$battery_perc" -le $effective_lower_threshold ]]; then
    echo "❌ This should NOT execute (lower threshold)"
    should_charge=true
    charge_reason="battery_low"
else
    echo "❌ This should NOT execute (normal range)"
fi

echo ""
echo "Final decision variables:"
echo "  should_charge=$should_charge"
echo "  should_stop_charge=$should_stop_charge"
echo "  charge_reason=$charge_reason"

if [[ "$should_stop_charge" == true ]]; then
    echo "✅ CORRECT: Script will execute STOP charging logic"
elif [[ "$should_charge" == true ]]; then
    echo "❌ ERROR: Script will execute START charging logic (WRONG!)"
else
    echo "❌ ERROR: Script will do nothing (WRONG!)"
fi
