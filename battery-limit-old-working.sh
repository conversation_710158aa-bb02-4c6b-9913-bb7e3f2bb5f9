#!/bin/bash
#===========================================
# Smart Battery Management Script for macOS
# Enhanced Version
#===========================================

# Configuration
LOWER_THRESHOLD=55
UPPER_THRESHOLD=90
WEBHOOK_KEY="990B939B49"
LOG_FILE="/tmp/battery_management.log"
STATE_FILE="/tmp/battery_state.txt"

# Get battery percentage
get_battery_percentage() {
    battery_info=$(pmset -g batt)
    if [[ "$battery_info" =~ ([0-9]+)% ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo "50"  # Default value if unable to get battery percentage
    fi
}

# Trigger webhook function
trigger_webhook() {
    local action=$1
    if [[ "$action" == "on" ]]; then
        curl -s -X POST "https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66748/switch_on" >/dev/null 2>&1
    elif [[ "$action" == "off" ]]; then
        curl -s -X POST "https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66750/switch_off" >/dev/null 2>&1
    fi
}

# Log message function
log_message() {
    local message=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" >> "$LOG_FILE"
}

# Send notification function
send_notification() {
    local message=$1
    osascript -e "display notification \"$message\" with title \"Battery Management\""
}

# Start sleep protection
start_sleep_protection() {
    sudo pmset -a sleep 0
    sudo pmset -a hibernatemode 0
    sudo pmset -a disablesleep 1
    log_message "Sleep protection enabled"
}

# Stop sleep protection
stop_sleep_protection() {
    sudo pmset -a sleep 1
    sudo pmset -a hibernatemode 3
    sudo pmset -a disablesleep 0
    log_message "Sleep protection disabled"
}

# Main function
manage_battery() {
    battery_perc=$(get_battery_percentage)
    previous_state=$(cat "$STATE_FILE" 2>/dev/null || echo "")

    if [ "$battery_perc" -le "$LOWER_THRESHOLD" ]; then
        if [ "$previous_state" != "charging_on" ]; then
            stop_sleep_protection
            trigger_webhook "on"
            send_notification "Battery low (${battery_perc}%). Charging started."
            echo "charging_on" > "$STATE_FILE"
        fi
    elif [ "$battery_perc" -ge "$UPPER_THRESHOLD" ]; then
        if [ "$previous_state" != "charging_off" ]; then
            start_sleep_protection
            sleep 2
            trigger_webhook "off"
            send_notification "Battery high (${battery_perc}%). Charging stopped."
            echo "charging_off" > "$STATE_FILE"
        fi
    else
        if [ "$previous_state" != "battery_normal" ]; then
            log_message "Battery level at ${battery_perc}%"
            echo "battery_normal" > "$STATE_FILE"
        fi
    fi
}

manage_battery
