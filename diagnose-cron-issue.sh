#!/bin/bash

echo "🔍 Diagnosing Cron Execution Issues"
echo "==================================="

echo "1. Checking if cron service is running:"
echo "======================================="
# Check if cron daemon is running
if pgrep -x "cron" >/dev/null; then
    echo "✅ cron daemon is running"
else
    echo "❌ cron daemon is NOT running"
    echo "   On macOS, cron might be replaced by launchd"
fi

echo ""
echo "2. Checking crontab permissions:"
echo "==============================="
# Check if user has crontab permissions
if crontab -l >/dev/null 2>&1; then
    echo "✅ User has crontab access"
    echo "Current crontab entries:"
    crontab -l 2>/dev/null || echo "No crontab entries found"
else
    echo "❌ User does NOT have crontab access"
    echo "   This might be due to macOS security restrictions"
fi

echo ""
echo "3. Checking macOS Full Disk Access:"
echo "==================================="
echo "⚠️  On macOS Catalina and later, Terminal/cron needs Full Disk Access"
echo "   Go to: System Preferences > Security & Privacy > Privacy > Full Disk Access"
echo "   Add: /usr/sbin/cron and/or Terminal.app"

echo ""
echo "4. Testing script execution manually:"
echo "===================================="
SCRIPT_PATH="/Users/<USER>/.scripts/battery-limit.sh"
if [[ -f "$SCRIPT_PATH" ]]; then
    echo "✅ Script exists at $SCRIPT_PATH"
    
    # Test script permissions
    if [[ -x "$SCRIPT_PATH" ]]; then
        echo "✅ Script is executable"
    else
        echo "❌ Script is NOT executable"
        echo "   Run: chmod +x $SCRIPT_PATH"
    fi
    
    # Test script syntax
    if bash -n "$SCRIPT_PATH" 2>/dev/null; then
        echo "✅ Script syntax is valid"
    else
        echo "❌ Script has syntax errors:"
        bash -n "$SCRIPT_PATH"
    fi
    
    echo ""
    echo "Testing manual execution with cron environment:"
    echo "=============================================="
    
    # Create log directory first
    mkdir -p "/Users/<USER>/.battery_health_manager"
    
    # Test with minimal cron-like environment
    echo "Running: env -i HOME=/Users/<USER>/usr/bin:/bin bash $SCRIPT_PATH manage --debug-env"
    
    # Run with timeout to prevent hanging
    timeout 30 env -i HOME="/Users/<USER>" PATH="/usr/bin:/bin" bash "$SCRIPT_PATH" manage --debug-env 2>&1 | head -20
    
else
    echo "❌ Script NOT found at $SCRIPT_PATH"
fi

echo ""
echo "5. Checking for alternative scheduling methods:"
echo "=============================================="
echo "On macOS, consider using launchd instead of cron:"
echo "   - More reliable on modern macOS"
echo "   - Better integration with system security"
echo "   - Automatic restart on failure"

echo ""
echo "6. Creating a simple test cron job:"
echo "=================================="
echo "Let's create a simple test to verify cron is working:"

# Create a simple test script
TEST_SCRIPT="/Users/<USER>/.scripts/cron-test.sh"
cat > "$TEST_SCRIPT" << 'EOF'
#!/bin/bash
echo "$(date): Cron test executed" >> /Users/<USER>/.scripts/cron-test.log
echo "HOME: $HOME" >> /Users/<USER>/.scripts/cron-test.log
echo "PATH: $PATH" >> /Users/<USER>/.scripts/cron-test.log
echo "PWD: $(pwd)" >> /Users/<USER>/.scripts/cron-test.log
echo "---" >> /Users/<USER>/.scripts/cron-test.log
EOF

chmod +x "$TEST_SCRIPT"

echo "Created test script: $TEST_SCRIPT"
echo "To test cron, run:"
echo "  (crontab -l 2>/dev/null; echo '* * * * * /usr/bin/bash $TEST_SCRIPT') | crontab -"
echo "  Wait 2 minutes, then check: cat /Users/<USER>/.scripts/cron-test.log"

echo ""
echo "7. Checking system logs for cron errors:"
echo "========================================"
echo "Recent cron-related system logs:"
log show --predicate 'process == "cron" OR eventMessage CONTAINS "cron"' --last 10m 2>/dev/null | tail -10 || echo "No recent cron logs found"

echo ""
echo "8. Alternative: Using launchd (recommended for macOS):"
echo "===================================================="
echo "Create a launchd plist file instead of using cron:"

PLIST_PATH="/Users/<USER>/Library/LaunchAgents/com.battery.health.plist"
echo "Plist location: $PLIST_PATH"

cat << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.battery.health</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/bin/bash</string>
        <string>/Users/<USER>/.scripts/battery-limit.sh</string>
        <string>manage</string>
        <string>--debug-env</string>
    </array>
    <key>StartInterval</key>
    <integer>60</integer>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/.battery_health_manager/launchd_errors.log</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/.battery_health_manager/launchd_output.log</string>
</dict>
</plist>
EOF

echo ""
echo "To use launchd instead:"
echo "1. Save the above XML to: $PLIST_PATH"
echo "2. Load it: launchctl load $PLIST_PATH"
echo "3. Start it: launchctl start com.battery.health"
