#!/bin/bash

echo "Quick External Monitor Test"
echo "=========================="

echo "Test 1: osascript desktop count"
if command -v osascript >/dev/null 2>&1; then
    screen_count=$(timeout 3 osascript -e 'tell application "System Events" to count desktops' 2>/dev/null || echo "1")
    echo "Desktop count: $screen_count"
    if [[ "$screen_count" -gt 1 ]]; then
        echo "✅ Multiple desktops detected - external monitor likely connected"
    else
        echo "❌ Only one desktop detected"
    fi
else
    echo "❌ osascript not available"
fi

echo ""
echo "Test 2: ioreg clamshell check"
if command -v ioreg >/dev/null 2>&1; then
    clamshell_result=$(timeout 3 ioreg -r -k AppleClamshellState -d 4 2>/dev/null | grep "AppleClamshellState")
    if [[ -n "$clamshell_result" ]]; then
        echo "Clamshell state found: $clamshell_result"
        if echo "$clamshell_result" | grep -q "Yes"; then
            echo "✅ Clamshell mode active - external monitor connected"
        else
            echo "❌ Clamshell mode not active"
        fi
    else
        echo "❌ No clamshell state information found"
    fi
else
    echo "❌ ioreg not available"
fi

echo ""
echo "Test 3: system_profiler display count (quick)"
if command -v system_profiler >/dev/null 2>&1; then
    display_count=$(timeout 5 system_profiler SPDisplaysDataType 2>/dev/null | grep -c "Resolution:" || echo "0")
    echo "Display count: $display_count"
    if [[ $display_count -gt 1 ]]; then
        echo "✅ Multiple displays detected"
    else
        echo "❌ Only one display detected"
    fi
else
    echo "❌ system_profiler not available"
fi

echo ""
echo "Test 4: pmset power source"
if command -v pmset >/dev/null 2>&1; then
    power_info=$(pmset -g batt 2>/dev/null | head -1)
    echo "Power source: $power_info"
else
    echo "❌ pmset not available"
fi

echo ""
echo "Summary:"
echo "========"
echo "If you have an external monitor connected, at least one test should show positive results."
