#!/bin/bash

echo "🔋 Battery Debug Information"
echo "============================"

echo "1. Raw pmset -g batt output:"
echo "----------------------------"
pmset -g batt 2>/dev/null || echo "pmset -g batt failed"

echo ""
echo "2. Raw pmset -g ps output:"
echo "--------------------------"
pmset -g ps 2>/dev/null || echo "pmset -g ps failed"

echo ""
echo "3. Parsing battery percentage:"
echo "------------------------------"
battery_info=$(pmset -g batt 2>/dev/null)
echo "Full battery info: $battery_info"

if [[ "$battery_info" =~ ([0-9]+)% ]]; then
    echo "Battery percentage found: ${BASH_REMATCH[1]}%"
else
    echo "❌ Could not parse battery percentage"
fi

echo ""
echo "4. Parsing charging state:"
echo "--------------------------"
echo "Checking for 'AC Power': $(echo "$battery_info" | grep -c "AC Power")"
echo "Checking for 'charging': $(echo "$battery_info" | grep -c "charging")"
echo "Checking for 'discharging': $(echo "$battery_info" | grep -c "discharging")"
echo "Checking for 'Battery Power': $(echo "$battery_info" | grep -c "Battery Power")"

if echo "$battery_info" | grep -q "AC Power"; then
    echo "✅ On AC Power"
    if echo "$battery_info" | grep -q "charging"; then
        echo "✅ Currently charging"
    else
        echo "⚡ Plugged in but not charging"
    fi
elif echo "$battery_info" | grep -q "Battery Power"; then
    echo "🔋 On Battery Power (discharging)"
else
    echo "❓ Unknown power state"
fi

echo ""
echo "5. System power adapter info:"
echo "-----------------------------"
system_profiler SPPowerDataType 2>/dev/null | grep -A5 -B5 "Connected:" | head -10 || echo "Could not get power adapter info"

echo ""
echo "6. Current power management settings:"
echo "------------------------------------"
pmset -g 2>/dev/null | head -10 || echo "Could not get power settings"
