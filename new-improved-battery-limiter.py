#!/usr/bin/env python3
"""
Smart Battery Management Script for macOS
Enhanced Version - Python Implementation
"""

import os
import sys
import json
import asyncio
import aiohttp
import psutil
from functools import cached_property
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
import logging
import subprocess
from typing import Dict, Optional, Tuple

# Configuration
CONFIG = {
    'LOWER_THRESHOLD': 70,
    'UPPER_THRESHOLD': 90,
    'WEBHOOK_KEY': '990B939B49',
    'LOG_FILE': '/tmp/battery_management_py.log',
    'STATE_FILE': '/tmp/battery_state_py.json'
}

# Configure logging
logging.basicConfig(
    filename=CONFIG['LOG_FILE'],
    level=logging.INFO,
    format='[%(asctime)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

class BatteryManager:
    def __init__(self):
        self.states = {
            'monitor': 'disconnected',
            'charging': 'off',
            'battery': 'normal'
        }
        self.load_states()
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.session = None
        self.battery = psutil.sensors_battery()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
        self.executor.shutdown(wait=False)

    @cached_property
    def battery_info(self):
        return psutil.sensors_battery()

    async def get_battery_percentage(self) -> int:
        """Get current battery percentage using psutil."""
        try:
            if self.battery_info:
                return int(self.battery_info.percent)
            return self._fallback_battery_percentage()
        except Exception as e:
            logging.error(f"Error getting battery percentage: {e}")
            return self._fallback_battery_percentage()

    def _fallback_battery_percentage(self) -> int:
        """Fallback method using pmset."""
        try:
            cmd = ['pmset', '-g', 'batt']
            output = subprocess.check_output(cmd).decode('utf-8')
            if '%' in output:
                return int(output.split('%')[0].split('\t')[-1])
            return 50  # Default value
        except Exception as e:
            logging.error(f"Error getting battery percentage: {e}")
            return 50

    async def trigger_webhook(self, action: str) -> None:
        """Trigger IFTTT webhook using aiohttp."""
        try:
            webhook_id = '66748' if action == 'on' else '66750'
            url = f"https://sequematic.com/trigger-ifttt-webhook/{CONFIG['WEBHOOK_KEY']}/{webhook_id}/switch_{action}"
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(url) as response:
                if response.status != 200:
                    logging.error(f"Webhook failed with status {response.status}")
        except Exception as e:
            logging.error(f"Error triggering webhook: {e}")

    async def send_notification(self, message: str) -> None:
        """Send macOS notification."""
        try:
            cmd = ['osascript', '-e', f'display notification "{message}" with title "Battery Management"']
            subprocess.run(cmd, capture_output=True)
        except Exception as e:
            logging.error(f"Error sending notification: {e}")

    def send_notification_sync(self, message: str) -> None:
        """Synchronous version of notification sender."""
        try:
            cmd = ['osascript', '-e', f'display notification "{message}" with title "Battery Management"']
            subprocess.run(cmd, capture_output=True)
        except Exception as e:
            logging.error(f"Error sending notification: {e}")

    def _execute_sleep_protection(self, enable: bool) -> None:
        """Execute sleep protection commands in separate thread."""
        try:
            settings = [
                ('sleep', '0' if enable else '1'),
                ('hibernatemode', '0' if enable else '3'),
                ('disablesleep', '1' if enable else '0')
            ]
            
            for setting, value in settings:
                subprocess.run(['sudo', 'pmset', '-a', setting, value], check=True)
            
            status = subprocess.check_output(['pmset', '-g', 'custom']).decode()
            sleep_status = next((line.split()[1] for line in status.split('\n') 
                               if line.strip().startswith('sleep')), 'unknown')
            
            state = "enabled" if enable else "disabled"
            message = f"Sleep protection {state}. Sleep status: {sleep_status}"
            logging.info(message)
            # Use synchronous notification for non-async context
            self.send_notification_sync(message)
            
        except Exception as e:
            logging.error(f"Error managing sleep protection: {e}")

    async def manage_sleep_protection(self, enable: bool) -> None:
        """Manage sleep protection settings asynchronously."""
        try:
            # Run pmset commands in thread pool to avoid blocking
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._execute_sleep_protection,
                enable
            )
            # Send notification through async method after sleep protection is managed
            state = "enabled" if enable else "disabled"
            await self.send_notification(f"Sleep protection {state}")
        except Exception as e:
            logging.error(f"Error managing sleep protection: {e}")

    def is_external_monitor_connected(self) -> bool:
        """Check if external monitor is connected based on resolution and connection type."""
        system_profiler_path = '/usr/sbin/system_profiler'
        try:
            cmd = [system_profiler_path, 'SPDisplaysDataType']
            output = subprocess.check_output(cmd, stderr=subprocess.PIPE).decode('utf-8')
            
            displays = []
            current_display = None
            in_displays_section = False
            
            for line in output.split('\n'):
                line = line.strip()
                
                # Start of Displays section
                if 'Displays:' in line:
                    in_displays_section = True
                    continue
                
                if in_displays_section:
                    # New display entry
                    if line.endswith(':') and not any(x in line for x in ['Chipset', 'Type', 'Bus', 'Vendor', 'Graphics']):
                        if current_display:
                            displays.append(current_display)
                        current_display = {
                            'name': line.rstrip(':'),
                            'resolution': None,
                            'connection_type': None,
                            'is_main': False
                        }
                    elif current_display:
                        # Parse display properties
                        if 'Resolution:' in line:
                            current_display['resolution'] = line.split('Resolution:')[1].strip()
                        elif 'Connection Type: Internal' in line:
                            current_display['connection_type'] = 'internal'
                        elif 'Display Type: Built-In' in line:
                            current_display['connection_type'] = 'internal'
                        elif 'Main Display: Yes' in line:
                            current_display['is_main'] = True

            # Add the last display if exists
            if current_display:
                displays.append(current_display)
            
            # Log display information
            for display in displays:
                logging.info(f"Found display: {display['name']}")
                logging.info(f"  Resolution: {display['resolution']}")
                logging.info(f"  Connection: {display['connection_type']}")
                logging.info(f"  Is Main: {display['is_main']}")
            
            # Check for external monitor
            external_displays = [
                d for d in displays 
                if d['connection_type'] != 'internal' 
                and d['resolution'] 
                and '2560 x 1600' not in d['resolution']  # Not the built-in display resolution
            ]
            
            if external_displays:
                main_external = any(d['is_main'] for d in external_displays)
                logging.info(f"External monitor{' (Main)' if main_external else ''} detected: {external_displays[0]['name']}")
                return True
            
            logging.info("No external monitor detected")
            return False
            
        except Exception as e:
            logging.error(f"Error checking external monitor: {str(e)}")
            return False

    def load_states(self) -> None:
        """Load states from state file."""
        try:
            if os.path.exists(CONFIG['STATE_FILE']):
                with open(CONFIG['STATE_FILE'], 'r') as f:
                    self.states = json.load(f)
        except Exception as e:
            logging.error(f"Error loading states: {e}")

    async def save_states(self) -> None:
        """Save states to state file."""
        try:
            with open(CONFIG['STATE_FILE'], 'w') as f:
                json.dump(self.states, f)
        except Exception as e:
            logging.error(f"Error saving states: {e}")

    async def manage_battery(self) -> None:
        """Main battery management logic with async support."""
        try:
            battery_perc = await self.get_battery_percentage()
            
            # Run monitor check and battery management concurrently
            await asyncio.gather(
                self._manage_monitor_state(),
                self._manage_battery_state(battery_perc)
            )
            
            await self.save_states()

        except Exception as e:
            logging.error(f"Error in manage_battery: {e}")

    async def _manage_monitor_state(self):
        """Manage monitor state asynchronously."""
        try:
            is_connected = await asyncio.get_event_loop().run_in_executor(
                self.executor, self.is_external_monitor_connected
            )
            
            if is_connected:
                if self.states['monitor'] != 'connected':
                    await self.manage_sleep_protection(True)
                    await self.send_notification("External monitor connected. Sleep protection enabled.")
                    logging.info("External monitor connected. Sleep protection enabled.")
                    self.states['monitor'] = 'connected'
            else:
                if self.states['monitor'] != 'disconnected':
                    await self.manage_sleep_protection(False)
                    await self.send_notification("External monitor disconnected. Sleep protection disabled.")
                    logging.info("External monitor disconnected. Sleep protection disabled.")
                    self.states['monitor'] = 'disconnected'
        except Exception as e:
            logging.error(f"Error in monitor state management: {str(e)}")

    async def _manage_battery_state(self, battery_perc: int):
        """Manage battery state asynchronously."""
        # Manage charging based on battery levels
        if battery_perc <= CONFIG['LOWER_THRESHOLD']:
            if self.states['charging'] != 'on':
                await self.trigger_webhook('on')
                await self.send_notification(f"Battery low ({battery_perc}%). Charging started.")
                logging.info(f"Charging started at {battery_perc}%.")
                self.states['charging'] = 'on'
        elif battery_perc >= CONFIG['UPPER_THRESHOLD']:
            if self.states['charging'] != 'off':
                await self.trigger_webhook('off')
                await self.send_notification(f"Battery high ({battery_perc}%). Charging stopped.")
                logging.info(f"Charging stopped at {battery_perc}%.")
                self.states['charging'] = 'off'
        else:
            if self.states['battery'] != 'normal':
                logging.info(f"Battery level at {battery_perc}%.")
                self.states['battery'] = 'normal'

async def main():
    async with BatteryManager() as manager:
        await manager.manage_battery()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logging.error(f"Error in main: {e}")
        sys.exit(1)

