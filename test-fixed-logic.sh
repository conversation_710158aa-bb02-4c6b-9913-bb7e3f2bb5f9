#!/bin/bash

echo "🔧 Testing Fixed Battery Logic at 50%"
echo "====================================="

# Get current battery percentage
battery_info=$(pmset -g batt 2>/dev/null)
if [[ "$battery_info" =~ ([0-9]+)% ]]; then
    current_battery="${BASH_REMATCH[1]}"
    echo "✅ Current battery: ${current_battery}%"
else
    echo "❌ Could not detect battery percentage"
    current_battery=50
fi

echo ""
echo "🧪 Testing Logic with Current Battery Level:"
echo "Battery: ${current_battery}%"
echo "Expected behavior:"
if [[ $current_battery -le 70 ]]; then
    echo "  ✅ ${current_battery}% <= 70% → Should START charging (send 'on' webhook)"
elif [[ $current_battery -ge 90 ]]; then
    echo "  ✅ ${current_battery}% >= 90% → Should STOP charging (send 'off' webhook)"
else
    echo "  ✅ ${current_battery}% in normal range (70%-90%) → No action needed"
fi

echo ""
echo "🚀 Running Fixed Script:"
echo "========================"

# Create config directory
mkdir -p "$HOME/.battery_health_manager"

# Clear previous logs to see fresh output
if [[ -f "$HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "Clearing previous logs..."
    > "$HOME/.battery_health_manager/battery_health.log"
fi

# Run the script
cd /Users/<USER>/.scripts
echo "Executing: ./battery-limit.sh manage"
./battery-limit.sh manage

echo ""
echo "📋 Analyzing Log Output:"
echo "========================"

if [[ -f "$HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "Full log output:"
    cat "$HOME/.battery_health_manager/battery_health.log"
    
    echo ""
    echo "🔍 Key Analysis:"
    echo "================"
    
    # Check battery detection
    if grep -q "Successfully parsed battery percentage" "$HOME/.battery_health_manager/battery_health.log"; then
        detected_battery=$(grep "Successfully parsed battery percentage" "$HOME/.battery_health_manager/battery_health.log" | tail -1 | grep -o '[0-9]\+%')
        echo "✅ Battery detected: $detected_battery"
    else
        echo "❌ Battery detection failed"
    fi
    
    # Check threshold evaluation
    if grep -q "Lower Threshold:" "$HOME/.battery_health_manager/battery_health.log"; then
        thresholds=$(grep "Lower Threshold:\|Upper Threshold:" "$HOME/.battery_health_manager/battery_health.log" | tail -2)
        echo "✅ Thresholds: $thresholds"
    fi
    
    # Check mode detection
    if grep -q "Travel Mode:\|Sailing Mode:" "$HOME/.battery_health_manager/battery_health.log"; then
        modes=$(grep "Travel Mode:\|Sailing Mode:" "$HOME/.battery_health_manager/battery_health.log" | tail -1)
        echo "✅ Modes: $modes"
    fi
    
    # Check logic evaluation
    if grep -q "Evaluating: Is.*<= .*lower threshold" "$HOME/.battery_health_manager/battery_health.log"; then
        echo "✅ Lower threshold evaluation found"
        if grep -q "YES:.*<= .*SHOULD START CHARGING" "$HOME/.battery_health_manager/battery_health.log"; then
            echo "✅ Correct decision: START charging"
        else
            echo "❌ Wrong decision or logic error"
        fi
    fi
    
    # Check webhook execution
    if grep -q "EXECUTING CHARGE START" "$HOME/.battery_health_manager/battery_health.log"; then
        echo "✅ Correct action: START charging"
        if grep -q "trigger_webhook.*on" "$HOME/.battery_health_manager/battery_health.log"; then
            echo "✅ Correct webhook: 'on' webhook sent"
        else
            echo "❌ Wrong webhook or webhook failed"
        fi
    elif grep -q "EXECUTING CHARGE STOP" "$HOME/.battery_health_manager/battery_health.log"; then
        echo "❌ Wrong action: STOP charging (should be START at low battery)"
    else
        echo "❓ No charging action taken"
    fi
    
else
    echo "❌ No log file found at $HOME/.battery_health_manager/battery_health.log"
fi

echo ""
echo "📊 Current State:"
echo "================="
if [[ -f "$HOME/.battery_health_manager/battery_state.json" ]]; then
    echo "State file contents:"
    cat "$HOME/.battery_health_manager/battery_state.json" | jq . 2>/dev/null || cat "$HOME/.battery_health_manager/battery_state.json"
else
    echo "❌ No state file found"
fi
