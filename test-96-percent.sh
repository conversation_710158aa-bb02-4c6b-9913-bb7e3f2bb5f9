#!/bin/bash

echo "🔋 Testing Battery Management at 96%"
echo "===================================="

# Get current battery info
battery_info=$(pmset -g batt 2>/dev/null)
echo "Current battery info: $battery_info"

# Parse battery percentage
if [[ "$battery_info" =~ ([0-9]+)% ]]; then
    battery_perc="${BASH_REMATCH[1]}"
    echo "✅ Current battery: ${battery_perc}%"
else
    echo "❌ Could not parse battery percentage"
    battery_perc=96  # Assume for testing
fi

echo ""
echo "🧪 Logic Test:"
echo "Battery: ${battery_perc}%"
echo "Expected: Since ${battery_perc}% >= 90%, should STOP charging"

echo ""
echo "🚀 Running actual battery management script:"
echo "============================================="

cd /Users/<USER>/.scripts
./battery-limit.sh manage

echo ""
echo "📋 Recent logs (last 20 lines):"
echo "================================"
if [[ -f "$HOME/.config/battery-health/battery.log" ]]; then
    tail -20 "$HOME/.config/battery-health/battery.log"
else
    echo "❌ Log file not found at $HOME/.config/battery-health/battery.log"
fi

echo ""
echo "📊 Current state:"
echo "================="
if [[ -f "$HOME/.config/battery-health/state.json" ]]; then
    echo "State file contents:"
    cat "$HOME/.config/battery-health/state.json" | jq . 2>/dev/null || cat "$HOME/.config/battery-health/state.json"
else
    echo "❌ State file not found"
fi

echo ""
echo "🔍 Key Questions to Answer:"
echo "1. Did the script detect battery at ${battery_perc}%?"
echo "2. Did it recognize ${battery_perc}% >= 90% (upper threshold)?"
echo "3. Did it decide to STOP charging (not START)?"
echo "4. Did it send the 'off' webhook successfully?"
echo "5. Did it update the state to 'not_charging'?"
