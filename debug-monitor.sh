#!/bin/bash
#===========================================
# External Monitor Detection Debug Script
#===========================================

echo "🔍 External Monitor Detection Debug"
echo "===================================="

# Method 1: system_profiler display count
echo "Method 1: system_profiler display count"
echo "---------------------------------------"
if command -v system_profiler >/dev/null 2>&1; then
    echo "Running: system_profiler SPDisplaysDataType | grep 'Resolution:'"
    display_count=$(timeout 10 system_profiler SPDisplaysDataType 2>/dev/null | grep -c "Resolution:" || echo "0")
    echo "Display count: $display_count"
    
    echo ""
    echo "Full display info:"
    timeout 10 system_profiler SPDisplaysDataType 2>/dev/null | head -20
else
    echo "system_profiler not available"
fi

echo ""
echo "Method 2: ioreg display check"
echo "-----------------------------"
if command -v ioreg >/dev/null 2>&1; then
    echo "Running: ioreg -l -w 0 | grep IODisplayConnect"
    ioreg -l -w 0 2>/dev/null | grep "IODisplayConnect" | head -5
    
    echo ""
    echo "Running: ioreg -l -w 0 | grep AppleDisplay"
    ioreg -l -w 0 2>/dev/null | grep "AppleDisplay" | head -5
else
    echo "ioreg not available"
fi

echo ""
echo "Method 3: Clamshell mode check"
echo "------------------------------"
if command -v ioreg >/dev/null 2>&1; then
    echo "Running: ioreg -r -k AppleClamshellState -d 4"
    clamshell_result=$(ioreg -r -k AppleClamshellState -d 4 2>/dev/null | grep "AppleClamshellState")
    if [[ -n "$clamshell_result" ]]; then
        echo "Clamshell state: $clamshell_result"
    else
        echo "No clamshell state found"
    fi
else
    echo "ioreg not available"
fi

echo ""
echo "Method 4: AppleScript screen bounds"
echo "-----------------------------------"
if command -v osascript >/dev/null 2>&1; then
    echo "Running: osascript to get screen bounds"
    screen_info=$(osascript -e 'tell application "System Events" to get the bounds of every desktop' 2>/dev/null || echo "")
    if [[ -n "$screen_info" ]]; then
        echo "Screen bounds: $screen_info"
        screen_count=$(echo "$screen_info" | grep -o "{" | wc -l | tr -d ' ')
        echo "Screen count: $screen_count"
    else
        echo "Could not get screen bounds"
    fi
else
    echo "osascript not available"
fi

echo ""
echo "Method 5: pmset display sleep settings"
echo "--------------------------------------"
if command -v pmset >/dev/null 2>&1; then
    echo "Running: pmset -g"
    pmset -g 2>/dev/null | grep -E "(displaysleep|sleep)"
else
    echo "pmset not available"
fi

echo ""
echo "Method 6: Current display configuration"
echo "--------------------------------------"
if command -v system_profiler >/dev/null 2>&1; then
    echo "Detailed display analysis:"
    display_output=$(timeout 10 system_profiler SPDisplaysDataType 2>/dev/null)
    
    echo "Total displays with resolution:"
    echo "$display_output" | grep -c "Resolution:" || echo "0"
    
    echo "Built-in displays:"
    echo "$display_output" | grep -c -E "(Built-in|Internal)" || echo "0"
    
    echo "Connection types found:"
    echo "$display_output" | grep -E "(Connection Type|Display Type)" | head -5
    
    echo "External connection indicators:"
    echo "$display_output" | grep -c -E "(DisplayPort|HDMI|USB-C|Thunderbolt|DVI)" || echo "0"
fi

echo ""
echo "🎯 Summary"
echo "=========="
echo "If you have an external monitor connected, we should see:"
echo "- Display count > 1"
echo "- External connection types (HDMI, DisplayPort, etc.)"
echo "- Multiple screen bounds"
echo "- Possibly clamshell mode if laptop lid is closed"
