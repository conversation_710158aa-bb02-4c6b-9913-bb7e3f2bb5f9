#!/bin/bash

echo "🔍 Testing Cron Environment Compatibility"
echo "=========================================="

# Set up the same environment as the script
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
export HOME="${HOME:-/Users/<USER>"

# Test basic commands
echo "Testing basic commands:"
echo "  whoami: $(whoami 2>/dev/null || echo 'FAILED')"
echo "  pwd: $(pwd 2>/dev/null || echo 'FAILED')"
echo "  date: $(date 2>/dev/null || echo 'FAILED')"

echo ""
echo "Testing macOS-specific commands:"
echo "  pmset available: $(command -v pmset >/dev/null 2>&1 && echo 'YES' || echo 'NO')"
echo "  curl available: $(command -v curl >/dev/null 2>&1 && echo 'YES' || echo 'NO')"

if command -v pmset >/dev/null 2>&1; then
    echo "  pmset -g batt: $(pmset -g batt 2>/dev/null | head -1 || echo 'FAILED')"
else
    echo "  pmset: NOT FOUND in PATH"
    echo "  Searching for pmset:"
    find /usr -name pmset 2>/dev/null | head -5
fi

echo ""
echo "Testing file system access:"
CONFIG_DIR="$HOME/.battery_health_manager"
echo "  Config dir: $CONFIG_DIR"
echo "  Can create dir: $(mkdir -p "$CONFIG_DIR" 2>/dev/null && echo 'YES' || echo 'NO')"
echo "  Can write file: $(echo 'test' > "$CONFIG_DIR/test.txt" 2>/dev/null && echo 'YES' || echo 'NO')"

if [[ -f "$CONFIG_DIR/test.txt" ]]; then
    rm "$CONFIG_DIR/test.txt"
fi

echo ""
echo "Testing network connectivity:"
echo "  Can reach sequematic.com: $(curl -s --connect-timeout 5 https://sequematic.com >/dev/null 2>&1 && echo 'YES' || echo 'NO')"

echo ""
echo "Environment variables:"
echo "  HOME: $HOME"
echo "  PATH: $PATH"
echo "  USER: ${USER:-'unset'}"
echo "  TERM: ${TERM:-'unset'}"
echo "  SHELL: ${SHELL:-'unset'}"

echo ""
echo "Testing script execution:"
SCRIPT_PATH="/Users/<USER>/.scripts/battery-limit.sh"
if [[ -f "$SCRIPT_PATH" ]]; then
    echo "  Script exists: YES"
    echo "  Script executable: $(test -x "$SCRIPT_PATH" && echo 'YES' || echo 'NO')"
    echo "  Script syntax check: $(bash -n "$SCRIPT_PATH" 2>/dev/null && echo 'OK' || echo 'SYNTAX ERROR')"
else
    echo "  Script exists: NO"
fi

echo ""
echo "🧪 Simulating cron execution:"
echo "============================="

# Simulate cron environment
unset TERM
export CRON_EXECUTION=1

if [[ -f "$SCRIPT_PATH" ]]; then
    echo "Running script in cron-like environment..."
    echo "Command: CRON_EXECUTION=1 bash $SCRIPT_PATH manage --debug-env"
    echo ""
    
    # Run with timeout to prevent hanging
    timeout 30 bash "$SCRIPT_PATH" manage --debug-env 2>&1 | head -50
    
    echo ""
    echo "Exit code: ${PIPESTATUS[0]}"
else
    echo "❌ Cannot test - script not found"
fi

echo ""
echo "📋 Log file check:"
echo "=================="
LOG_FILE="$HOME/.battery_health_manager/battery_health.log"
if [[ -f "$LOG_FILE" ]]; then
    echo "Log file exists: YES"
    echo "Recent entries:"
    tail -10 "$LOG_FILE"
else
    echo "Log file exists: NO"
fi
