[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "aiohttp", "kind": 6, "isExtraImport": true, "importPath": "aiohttp", "description": "aiohttp", "detail": "aiohttp", "documentation": {}}, {"label": "psutil", "kind": 6, "isExtraImport": true, "importPath": "psutil", "description": "psutil", "detail": "psutil", "documentation": {}}, {"label": "cached_property", "importPath": "functools", "description": "functools", "isExtraImport": true, "detail": "functools", "documentation": {}}, {"label": "ThreadPoolExecutor", "importPath": "concurrent.futures", "description": "concurrent.futures", "isExtraImport": true, "detail": "concurrent.futures", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "BatteryManager", "kind": 6, "importPath": "new-improved-battery-limiter", "description": "new-improved-battery-limiter", "peekOfCode": "class BatteryManager:\n    def __init__(self):\n        self.states = {\n            'monitor': 'disconnected',\n            'charging': 'off',\n            'battery': 'normal'\n        }\n        self.load_states()\n        self.executor = ThreadPoolExecutor(max_workers=3)\n        self.session = None", "detail": "new-improved-battery-limiter", "documentation": {}}, {"label": "CONFIG", "kind": 5, "importPath": "new-improved-battery-limiter", "description": "new-improved-battery-limiter", "peekOfCode": "CONFIG = {\n    'LOWER_THRESHOLD': 70,\n    'UPPER_THRESHOLD': 90,\n    'WEBHOOK_KEY': '990B939B49',\n    'LOG_FILE': '/tmp/battery_management_py.log',\n    'STATE_FILE': '/tmp/battery_state_py.json'\n}\n# Configure logging\nlogging.basicConfig(\n    filename=CONFIG['LOG_FILE'],", "detail": "new-improved-battery-limiter", "documentation": {}}]