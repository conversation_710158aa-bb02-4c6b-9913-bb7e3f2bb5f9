#!/bin/bash

echo "🔋 Running Battery Management Test"
echo "=================================="

# Create the config directory if it doesn't exist
mkdir -p "$HOME/.battery_health_manager"

# Run the battery management script
echo "Running: ./battery-limit.sh manage"
echo ""

cd /Users/<USER>/.scripts
./battery-limit.sh manage

echo ""
echo "📋 Checking logs:"
echo "=================="

if [[ -f "$HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "Last 30 lines from log:"
    tail -30 "$HOME/.battery_health_manager/battery_health.log"
else
    echo "❌ Log file not found at $HOME/.battery_health_manager/battery_health.log"
fi

echo ""
echo "📊 Checking state:"
echo "=================="

if [[ -f "$HOME/.battery_health_manager/battery_state.json" ]]; then
    echo "Current state:"
    cat "$HOME/.battery_health_manager/battery_state.json"
else
    echo "❌ State file not found"
fi

echo ""
echo "🔍 Manual battery check:"
echo "========================"
pmset -g batt
