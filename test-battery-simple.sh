#!/bin/bash

echo "Testing battery script execution..."

# Test 1: Check script syntax
echo "1. Checking script syntax..."
if bash -n battery-limit.sh; then
    echo "✅ Script syntax is valid"
else
    echo "❌ Script has syntax errors"
    exit 1
fi

# Test 2: Test help command (should be quick)
echo "2. Testing help command..."
if timeout 10 bash battery-limit.sh help >/dev/null 2>&1; then
    echo "✅ Help command works"
else
    echo "❌ Help command failed or timed out"
fi

# Test 3: Test config command (should be quick)
echo "3. Testing config command..."
if timeout 10 bash battery-limit.sh config >/dev/null 2>&1; then
    echo "✅ Config command works"
else
    echo "❌ Config command failed or timed out"
fi

# Test 4: Test status command with timeout
echo "4. Testing status command (with 15s timeout)..."
if timeout 15 bash battery-limit.sh status >/dev/null 2>&1; then
    echo "✅ Status command completed"
else
    echo "❌ Status command failed or timed out"
fi

# Test 5: Check if external monitor detection is working
echo "5. Testing external monitor detection..."
echo "Checking desktop count with osascript..."
if command -v osascript >/dev/null 2>&1; then
    desktop_count=$(timeout 5 osascript -e 'tell application "System Events" to count desktops' 2>/dev/null || echo "1")
    echo "Desktop count: $desktop_count"
    if [[ "$desktop_count" -gt 1 ]]; then
        echo "✅ Multiple desktops detected - external monitor connected"
    else
        echo "ℹ️  Single desktop detected"
    fi
else
    echo "❌ osascript not available"
fi

echo ""
echo "Test completed!"
