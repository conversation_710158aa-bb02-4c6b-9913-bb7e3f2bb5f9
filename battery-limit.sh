#!/bin/bash
#===========================================
# Advanced Battery Health Management Script for macOS Tahoe
# AlDente Pro-like Features Implementation
# Version 2.0 - Enhanced with Advanced Battery Health Automation
#===========================================

# Script metadata
SCRIPT_VERSION="2.0.0"
SCRIPT_NAME="Advanced Battery Health Manager"
LAST_UPDATED="2025-06-27"

# Core Configuration Files
CONFIG_DIR="$HOME/.battery_health_manager"
CONFIG_FILE="$CONFIG_DIR/config.json"
LOG_FILE="$CONFIG_DIR/battery_health.log"
STATE_FILE="$CONFIG_DIR/battery_state.json"
HEALTH_LOG_FILE="$CONFIG_DIR/battery_health_history.log"
SCHEDULE_FILE="$CONFIG_DIR/charging_schedule.json"
TEMP_DIR="/tmp/battery_manager"

# Default Configuration (will be overridden by config file)
DEFAULT_LOWER_THRESHOLD=70
DEFAULT_UPPER_THRESHOLD=90
DEFAULT_TRAVEL_THRESHOLD=100
DEFAULT_SAILING_THRESHOLD=50
DEFAULT_TEMP_WARNING=45
DEFAULT_TEMP_CRITICAL=55
WEBHOOK_KEY="990B939B49"

# Create necessary directories
mkdir -p "$CONFIG_DIR" "$TEMP_DIR"

# Configuration Management
load_config() {
    # Initialize with defaults
    LOWER_THRESHOLD=$DEFAULT_LOWER_THRESHOLD
    UPPER_THRESHOLD=$DEFAULT_UPPER_THRESHOLD
    TRAVEL_THRESHOLD=$DEFAULT_TRAVEL_THRESHOLD
    SAILING_THRESHOLD=$DEFAULT_SAILING_THRESHOLD
    TEMP_WARNING=$DEFAULT_TEMP_WARNING
    TEMP_CRITICAL=$DEFAULT_TEMP_CRITICAL

    # Load from config file if exists
    if [[ -f "$CONFIG_FILE" ]]; then
        # Parse JSON config (basic implementation)
        while IFS= read -r line; do
            if [[ $line =~ \"([^\"]+)\":[[:space:]]*([0-9]+) ]]; then
                key="${BASH_REMATCH[1]}"
                value="${BASH_REMATCH[2]}"
                case "$key" in
                "lower_threshold") LOWER_THRESHOLD=$value ;;
                "upper_threshold") UPPER_THRESHOLD=$value ;;
                "travel_threshold") TRAVEL_THRESHOLD=$value ;;
                "sailing_threshold") SAILING_THRESHOLD=$value ;;
                "temp_warning") TEMP_WARNING=$value ;;
                "temp_critical") TEMP_CRITICAL=$value ;;
                esac
            fi
        done <"$CONFIG_FILE"
    else
        create_default_config
    fi
}

create_default_config() {
    cat >"$CONFIG_FILE" <<EOF
{
    "version": "2.0",
    "profile_name": "default",
    "description": "Default battery health management profile",
    "lower_threshold": $DEFAULT_LOWER_THRESHOLD,
    "upper_threshold": $DEFAULT_UPPER_THRESHOLD,
    "travel_threshold": $DEFAULT_TRAVEL_THRESHOLD,
    "sailing_threshold": $DEFAULT_SAILING_THRESHOLD,
    "temp_warning": $DEFAULT_TEMP_WARNING,
    "temp_critical": $DEFAULT_TEMP_CRITICAL,
    "sailing_mode_enabled": true,
    "smart_charging_enabled": true,
    "thermal_protection_enabled": true,
    "schedule_enabled": false,
    "night_hours_start": 22,
    "night_hours_end": 6,
    "work_days_only": true,
    "predictive_charging_enabled": true,
    "notifications_enabled": true,
    "thermal_warnings": true,
    "health_warnings": true,
    "mode_changes": true,
    "charging_events": true,
    "detailed_logging": true,
    "clamshell_mode_enabled": true,
    "sleep_protection_enabled": true,
    "auto_detect_enabled": true,
    "webhook_enabled": true,
    "retry_attempts": 3,
    "timeout": 10,
    "url_on": "https://maker.ifttt.com/trigger/battery_charge_on/with/key/YOUR_KEY",
    "url_off": "https://maker.ifttt.com/trigger/battery_charge_off/with/key/YOUR_KEY"
}
EOF
    log_message "Created default configuration file at $CONFIG_FILE"

    # Create profiles directory and default profile
    mkdir -p "$CONFIG_DIR/profiles"
    cp "$CONFIG_FILE" "$CONFIG_DIR/profiles/default.json"
    echo "default" >"$CONFIG_DIR/active_profile"
}

# Advanced Configuration Management Functions
load_profile() {
    local profile_name=${1:-"default"}
    local profile_file="$CONFIG_DIR/profiles/${profile_name}.json"

    if [[ ! -f "$profile_file" ]]; then
        log_message "ERROR" "Profile '$profile_name' not found"
        return 1
    fi

    echo "$profile_name" >"$CONFIG_DIR/active_profile"
    CONFIG_FILE="$profile_file"
    load_config
    log_message "INFO" "Switched to profile: $profile_name"
    return 0
}

create_profile() {
    local profile_name=$1
    local description=${2:-"Custom battery management profile"}

    if [[ -z "$profile_name" ]]; then
        echo "Error: Profile name required"
        return 1
    fi

    local profile_file="$CONFIG_DIR/profiles/${profile_name}.json"

    if [[ -f "$profile_file" ]]; then
        echo "Error: Profile '$profile_name' already exists"
        return 1
    fi

    # Create new profile based on current configuration
    cat >"$profile_file" <<EOF
{
    "version": "2.0",
    "profile_name": "$profile_name",
    "description": "$description",
    "lower_threshold": ${LOWER_THRESHOLD:-$DEFAULT_LOWER_THRESHOLD},
    "upper_threshold": ${UPPER_THRESHOLD:-$DEFAULT_UPPER_THRESHOLD},
    "travel_threshold": ${TRAVEL_THRESHOLD:-$DEFAULT_TRAVEL_THRESHOLD},
    "sailing_threshold": ${SAILING_THRESHOLD:-$DEFAULT_SAILING_THRESHOLD},
    "temp_warning": ${TEMP_WARNING:-$DEFAULT_TEMP_WARNING},
    "temp_critical": ${TEMP_CRITICAL:-$DEFAULT_TEMP_CRITICAL},
    "sailing_mode_enabled": true,
    "smart_charging_enabled": true,
    "thermal_protection_enabled": true,
    "schedule_enabled": false,
    "night_hours_start": 22,
    "night_hours_end": 6,
    "work_days_only": true,
    "predictive_charging_enabled": true,
    "notifications_enabled": true,
    "thermal_warnings": true,
    "health_warnings": true,
    "mode_changes": true,
    "charging_events": true,
    "detailed_logging": true,
    "clamshell_mode_enabled": true,
    "sleep_protection_enabled": true,
    "auto_detect_enabled": true,
    "webhook_enabled": true,
    "retry_attempts": 3,
    "timeout": 10,
    "url_on": "https://maker.ifttt.com/trigger/battery_charge_on/with/key/YOUR_KEY",
    "url_off": "https://maker.ifttt.com/trigger/battery_charge_off/with/key/YOUR_KEY"
}
EOF

    echo "Profile '$profile_name' created successfully"
    log_message "INFO" "Created new profile: $profile_name"
    return 0
}

list_profiles() {
    echo "Available battery management profiles:"
    echo "======================================"

    local active_profile="default"
    if [[ -f "$CONFIG_DIR/active_profile" ]]; then
        active_profile=$(cat "$CONFIG_DIR/active_profile" 2>/dev/null || echo "default")
    fi

    if [[ -d "$CONFIG_DIR/profiles" ]]; then
        for profile_file in "$CONFIG_DIR/profiles"/*.json; do
            if [[ -f "$profile_file" ]]; then
                local profile_name=$(basename "$profile_file" .json)
                local description=$(grep -o '"description": *"[^"]*"' "$profile_file" | sed 's/.*"description": *"\([^"]*\)".*/\1/' 2>/dev/null || echo "No description")

                if [[ "$profile_name" == "$active_profile" ]]; then
                    echo "* $profile_name (ACTIVE) - $description"
                else
                    echo "  $profile_name - $description"
                fi
            fi
        done
    else
        echo "No profiles found. Creating default profile..."
        create_default_config
    fi
}

delete_profile() {
    local profile_name=$1

    if [[ -z "$profile_name" ]]; then
        echo "Error: Profile name required"
        return 1
    fi

    if [[ "$profile_name" == "default" ]]; then
        echo "Error: Cannot delete default profile"
        return 1
    fi

    local profile_file="$CONFIG_DIR/profiles/${profile_name}.json"

    if [[ ! -f "$profile_file" ]]; then
        echo "Error: Profile '$profile_name' not found"
        return 1
    fi

    # Check if it's the active profile
    local active_profile="default"
    if [[ -f "$CONFIG_DIR/active_profile" ]]; then
        active_profile=$(cat "$CONFIG_DIR/active_profile" 2>/dev/null || echo "default")
    fi

    if [[ "$profile_name" == "$active_profile" ]]; then
        echo "Switching to default profile before deletion..."
        load_profile "default"
    fi

    rm -f "$profile_file"
    echo "Profile '$profile_name' deleted successfully"
    log_message "INFO" "Deleted profile: $profile_name"
    return 0
}

# Error Handling and Fail-safes
set_error_trap() {
    trap 'handle_error $? $LINENO' ERR
    trap 'handle_exit' EXIT
    trap 'handle_interrupt' INT TERM
}

handle_error() {
    local exit_code=$1
    local line_number=$2
    log_message "ERROR" "Script error at line $line_number (exit code: $exit_code)"

    # Emergency battery protection
    emergency_battery_protection

    # Restore system state if needed
    restore_system_state

    exit $exit_code
}

handle_exit() {
    log_message "DEBUG" "Script exiting, performing cleanup..."
    cleanup_temp_files
    restore_system_state
}

handle_interrupt() {
    log_message "WARN" "Script interrupted by user"
    emergency_battery_protection
    restore_system_state
    exit 130
}

emergency_battery_protection() {
    log_message "WARN" "Activating emergency battery protection"

    local battery_perc=$(get_battery_percentage_safe)

    # Critical battery protection
    if [[ $battery_perc -lt 5 ]]; then
        log_message "CRITICAL" "Battery critically low ($battery_perc%), forcing charge"
        force_charging_on "emergency_low_battery"
        send_smart_notification "critical_battery" "$battery_perc" "Emergency charging activated"
    fi

    # Disable any active thermal protection that might prevent charging
    if [[ $battery_perc -lt 15 ]]; then
        set_state_value "thermal_protection_active" "false"
        log_message "WARN" "Disabled thermal protection due to low battery"
    fi

    # Reset any stuck charging states
    reset_charging_state
}

restore_system_state() {
    log_message "DEBUG" "Restoring system state..."

    # Restore sleep settings if they were modified
    local original_sleep=$(get_state_value "original_sleep_settings")
    if [[ -n "$original_sleep" && "$original_sleep" != "null" ]]; then
        pmset -c sleep "$original_sleep" 2>/dev/null || true
        log_message "DEBUG" "Restored original sleep settings: $original_sleep"
    fi

    # Clear any temporary state locks
    rm -f "$TEMP_DIR"/*.lock 2>/dev/null || true

    # Ensure webhook state is consistent
    verify_webhook_state
}

cleanup_temp_files() {
    # Clean up temporary files older than 1 hour
    find "$TEMP_DIR" -type f -mmin +60 -delete 2>/dev/null || true

    # Clean up old log entries (keep last 1000 lines)
    if [[ -f "$LOG_FILE" ]]; then
        tail -n 1000 "$LOG_FILE" >"$LOG_FILE.tmp" 2>/dev/null && mv "$LOG_FILE.tmp" "$LOG_FILE" || true
    fi
}

reset_charging_state() {
    log_message "DEBUG" "Resetting charging state"

    # Clear any stuck state values
    set_state_value "charging_override" "false"
    set_state_value "thermal_protection_active" "false"
    set_state_value "emergency_mode" "false"

    # Verify webhook connectivity
    verify_webhook_state
}

verify_webhook_state() {
    if [[ -n "$WEBHOOK_URL_ON" && "$WEBHOOK_URL_ON" != *"YOUR_KEY"* ]]; then
        # Test webhook connectivity with a simple ping
        local webhook_test=$(curl -s -m 5 --head "$WEBHOOK_URL_ON" 2>/dev/null | head -1 || echo "failed")
        if [[ "$webhook_test" == *"failed"* ]]; then
            log_message "WARN" "Webhook connectivity issue detected"
            set_state_value "webhook_error" "true"
        else
            set_state_value "webhook_error" "false"
        fi
    fi
}

get_battery_percentage_safe() {
    local battery_info
    local attempts=0
    local max_attempts=3

    while [[ $attempts -lt $max_attempts ]]; do
        battery_info=$(pmset -g batt 2>/dev/null)
        if [[ -n "$battery_info" && "$battery_info" =~ ([0-9]+)% ]]; then
            echo "${BASH_REMATCH[1]}"
            return 0
        fi

        ((attempts++))
        log_message "WARN" "Failed to get battery percentage (attempt $attempts/$max_attempts)"
        sleep 1
    done

    # Fallback method using system_profiler
    battery_info=$(system_profiler SPPowerDataType 2>/dev/null | grep -i "state of charge" | grep -o '[0-9]\+' | head -1)
    if [[ -n "$battery_info" ]]; then
        echo "$battery_info"
        return 0
    fi

    log_message "ERROR" "Could not determine battery percentage, assuming 50% for safety"
    echo "50"
    return 1
}

# Enhanced Battery Information Functions
get_battery_percentage() {
    local battery_info
    battery_info=$(pmset -g batt 2>/dev/null)

    # Debug logging
    log_message "DEBUG" "Raw battery info: $battery_info"

    # Try to extract percentage from pmset output
    if [[ "$battery_info" =~ ([0-9]+)% ]]; then
        local percentage="${BASH_REMATCH[1]}"
        log_message "DEBUG" "Battery percentage parsed: ${percentage}%"
        echo "$percentage"
    else
        log_message "WARN" "Could not parse battery percentage, using default"
        echo "50" # Default value if unable to get battery percentage
    fi
}

get_battery_temperature() {
    # Try multiple methods to get battery temperature
    local temp=""

    # Method 1: Using system_profiler (most reliable)
    if command -v system_profiler >/dev/null 2>&1; then
        temp=$(system_profiler SPPowerDataType 2>/dev/null | grep -i "temperature" | head -1 | grep -o '[0-9]\+' | head -1)
    fi

    # Method 2: Using pmset thermal log
    if [[ -z "$temp" ]]; then
        temp=$(pmset -g thermlog 2>/dev/null | grep -o 'CPU_Scheduler=[0-9]\+' | grep -o '[0-9]\+' | head -1)
        # Convert from thermal pressure to approximate temperature
        if [[ -n "$temp" && "$temp" -gt 0 ]]; then
            temp=$((temp / 10 + 30)) # Rough conversion
        fi
    fi

    # Method 3: Fallback using thermal state
    if [[ -z "$temp" ]]; then
        local thermal_state=$(pmset -g therm 2>/dev/null | grep -o '[0-9]\+')
        if [[ -n "$thermal_state" ]]; then
            case "$thermal_state" in
            0) temp=35 ;; # Normal
            1) temp=45 ;; # Fair
            2) temp=55 ;; # Serious
            3) temp=65 ;; # Critical
            *) temp=40 ;; # Default
            esac
        fi
    fi

    # Default temperature if all methods fail
    echo "${temp:-40}"
}

get_battery_health() {
    local health_info=$(system_profiler SPPowerDataType 2>/dev/null | grep -A 5 "Health Information")
    local max_capacity=$(echo "$health_info" | grep "Maximum Capacity" | grep -o '[0-9]\+' | head -1)
    local cycle_count=$(echo "$health_info" | grep "Cycle Count" | grep -o '[0-9]\+' | head -1)

    echo "${max_capacity:-100}:${cycle_count:-0}"
}

get_charging_state() {
    local battery_info
    battery_info=$(pmset -g batt 2>/dev/null)

    # Debug logging
    log_message "DEBUG" "Raw charging info: $battery_info"

    # Check for different power states and charging conditions
    if echo "$battery_info" | grep -q "AC Power"; then
        log_message "DEBUG" "On AC Power"
        if echo "$battery_info" | grep -q "charging"; then
            log_message "DEBUG" "Currently charging"
            echo "charging"
        elif echo "$battery_info" | grep -q "charged"; then
            log_message "DEBUG" "Fully charged"
            echo "charged"
        else
            log_message "DEBUG" "Plugged in but not charging"
            echo "plugged_not_charging"
        fi
    elif echo "$battery_info" | grep -q "Battery Power"; then
        log_message "DEBUG" "On Battery Power"
        echo "discharging"
    else
        log_message "WARN" "Unknown power state"
        echo "unknown"
    fi
}

get_power_source() {
    local power_info=$(pmset -g ps)
    if echo "$power_info" | grep -q "AC Power"; then
        echo "ac"
    else
        echo "battery"
    fi
}

# Enhanced Logging Functions
log_message() {
    local level=${1:-"INFO"}
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [$level] $message"

    echo "$log_entry" >>"$LOG_FILE"

    # Also log to system log for important messages
    if [[ "$level" == "ERROR" || "$level" == "CRITICAL" ]]; then
        logger -t "BatteryHealthManager" "$message"
    fi
}

log_battery_health() {
    local battery_perc=$1
    local temperature=$2
    local health_info=$3
    local charging_state=$4
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Log detailed health information
    echo "[$timestamp] HEALTH: Battery=$battery_perc%, Temp=${temperature}°C, Health=$health_info, State=$charging_state" >>"$HEALTH_LOG_FILE"
}

# Enhanced Notification System
send_notification() {
    local title=${1:-"Battery Management"}
    local message=$2
    local priority=${3:-"normal"}

    # Check if notifications are enabled
    if ! grep -q '"notifications_enabled": true' "$CONFIG_FILE" 2>/dev/null; then
        return 0
    fi

    # Send macOS notification
    osascript -e "display notification \"$message\" with title \"$title\"" 2>/dev/null

    # Log notification
    log_message "NOTIFICATION" "$title: $message"

    # For critical notifications, also use system alert
    if [[ "$priority" == "critical" ]]; then
        osascript -e "display alert \"$title\" message \"$message\" as critical" 2>/dev/null &
    fi
}

send_smart_notification() {
    local event_type=$1
    local battery_perc=$2
    local additional_info=${3:-""}

    case "$event_type" in
    "charging_started")
        send_notification "🔋 Charging Started" "Battery at ${battery_perc}%. Charging enabled. $additional_info" "normal"
        ;;
    "charging_stopped")
        send_notification "⚡ Charging Stopped" "Battery at ${battery_perc}%. Charging disabled to preserve health. $additional_info" "normal"
        ;;
    "thermal_warning")
        send_notification "🌡️ Thermal Warning" "Battery temperature high (${additional_info}°C). Monitoring closely." "warning"
        ;;
    "thermal_critical")
        send_notification "🚨 Thermal Critical" "Battery temperature critical (${additional_info}°C)! Stopping charge immediately." "critical"
        ;;
    "sailing_mode")
        send_notification "⛵ Sailing Mode" "Battery health maintenance: discharging to ${battery_perc}%. $additional_info" "normal"
        ;;
    "travel_mode")
        send_notification "✈️ Travel Mode" "Charging to ${battery_perc}% for travel. $additional_info" "normal"
        ;;
    "monitor_connected")
        send_notification "🖥️ External Monitor" "External display detected. Clamshell mode enabled. $additional_info" "normal"
        ;;
    "monitor_disconnected")
        send_notification "💻 Laptop Mode" "External display disconnected. Normal power management restored. $additional_info" "normal"
        ;;
    "health_warning")
        send_notification "⚠️ Battery Health" "Battery health declining. $additional_info" "warning"
        ;;
    *)
        send_notification "Battery Manager" "$event_type: $additional_info" "normal"
        ;;
    esac
}

# Enhanced Webhook Functions
trigger_webhook() {
    local action=$1
    local reason=${2:-"battery_management"}

    log_message "INFO" "Triggering webhook: $action (reason: $reason)"

    local webhook_id
    if [[ "$action" == "on" ]]; then
        webhook_id="66748"
    elif [[ "$action" == "off" ]]; then
        webhook_id="66750"
    else
        log_message "ERROR" "Invalid webhook action: $action"
        return 1
    fi

    local url="https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/$webhook_id/switch_$action"

    # Try webhook with timeout and retry logic
    local max_retries=3
    local retry_count=0

    while [[ $retry_count -lt $max_retries ]]; do
        if curl -s -m 10 -X POST "$url" >/dev/null 2>&1; then
            log_message "INFO" "Webhook $action successful (attempt $((retry_count + 1)))"
            return 0
        else
            retry_count=$((retry_count + 1))
            log_message "WARN" "Webhook $action failed (attempt $retry_count)"
            if [[ $retry_count -lt $max_retries ]]; then
                sleep 2
            fi
        fi
    done

    log_message "ERROR" "Webhook $action failed after $max_retries attempts"
    return 1
}

# Enhanced Sleep Protection and Power Management
manage_sleep_protection() {
    local enable=$1
    local reason=${2:-"external_monitor"}

    if [[ "$enable" == "true" ]]; then
        log_message "INFO" "Enabling sleep protection for $reason"

        # Enhanced sleep protection for clamshell mode
        sudo pmset -a sleep 0
        sudo pmset -a hibernatemode 0
        sudo pmset -a disablesleep 1
        sudo pmset -a standby 0
        sudo pmset -a autopoweroff 0

        # Verify sleep protection status
        local sleep_status=$(pmset -g custom | awk '/^ ?sleep/ {print $2}')
        log_message "INFO" "Sleep protection enabled. Sleep status: $sleep_status"

        # Store original power settings for restoration
        pmset -g custom >"$TEMP_DIR/original_power_settings.txt"

    else
        log_message "INFO" "Disabling sleep protection for $reason"

        # Restore normal power management
        sudo pmset -a sleep 1
        sudo pmset -a hibernatemode 3
        sudo pmset -a disablesleep 0
        sudo pmset -a standby 1
        sudo pmset -a autopoweroff 1

        # Verify sleep protection status
        local sleep_status=$(pmset -g custom | awk '/^ ?sleep/ {print $2}')
        log_message "INFO" "Sleep protection disabled. Sleep status: $sleep_status"
    fi
}

# Thermal Management Functions
check_thermal_state() {
    local temperature=$1
    local current_charging_state=$2

    if [[ $temperature -ge $TEMP_CRITICAL ]]; then
        log_message "CRITICAL" "Critical temperature detected: ${temperature}°C"
        send_smart_notification "thermal_critical" "" "$temperature"

        # Force stop charging immediately
        if [[ "$current_charging_state" == "charging" ]]; then
            trigger_webhook "off" "thermal_protection"
            log_message "CRITICAL" "Emergency charging stop due to critical temperature"
        fi

        return 2 # Critical
    elif [[ $temperature -ge $TEMP_WARNING ]]; then
        log_message "WARN" "High temperature detected: ${temperature}°C"
        send_smart_notification "thermal_warning" "" "$temperature"
        return 1 # Warning
    else
        return 0 # Normal
    fi
}

# Simplified External Monitor Detection for macOS Tahoe
is_external_monitor_connected() {
    log_message "DEBUG" "Checking for external monitor connection..."

    # Method 1: Quick AppleScript check for multiple screens
    local screen_count=1
    if command -v osascript >/dev/null 2>&1; then
        local screen_bounds
        screen_bounds=$(timeout 3 osascript -e 'tell application "System Events" to count desktops' 2>/dev/null || echo "1")
        if [[ "$screen_bounds" =~ ^[0-9]+$ ]] && [[ $screen_bounds -gt 1 ]]; then
            log_message "DEBUG" "Multiple screens detected via AppleScript ($screen_bounds screens)"
            return 0
        fi
    fi

    # Method 2: Check clamshell mode (most reliable indicator)
    if command -v ioreg >/dev/null 2>&1; then
        if timeout 3 ioreg -r -k AppleClamshellState -d 4 2>/dev/null | grep -q "AppleClamshellState.*Yes"; then
            log_message "DEBUG" "Clamshell mode active - external monitor connected"
            return 0
        fi
    fi

    # Method 3: Simple display count check
    if command -v system_profiler >/dev/null 2>&1; then
        local display_info
        display_info=$(timeout 5 system_profiler SPDisplaysDataType 2>/dev/null | grep "Resolution:" | wc -l | tr -d ' ')
        if [[ "$display_info" =~ ^[0-9]+$ ]] && [[ $display_info -gt 1 ]]; then
            log_message "DEBUG" "Multiple displays detected ($display_info total)"
            return 0
        fi
    fi

    # Method 4: Check for external connection keywords
    if command -v system_profiler >/dev/null 2>&1; then
        if timeout 5 system_profiler SPDisplaysDataType 2>/dev/null | grep -q -E "(HDMI|DisplayPort|USB-C|Thunderbolt|DVI|External)"; then
            log_message "DEBUG" "External connection type detected"
            return 0
        fi
    fi

    log_message "DEBUG" "No external monitor detected"
    return 1
}

# Alternative external monitor detection using multiple methods
detect_external_monitor_advanced() {
    local detection_methods=0
    local positive_detections=0

    # Method 1: system_profiler
    if system_profiler SPDisplaysDataType 2>/dev/null | grep -q -E "(Connection Type: (DisplayPort|HDMI|USB-C|Thunderbolt))"; then
        ((positive_detections++))
        log_message "DEBUG" "Method 1 (system_profiler): External monitor detected"
    fi
    ((detection_methods++))

    # Method 2: ioreg display check
    if ioreg -l 2>/dev/null | grep -E "IODisplayConnect.*External" >/dev/null; then
        ((positive_detections++))
        log_message "DEBUG" "Method 2 (ioreg): External monitor detected"
    fi
    ((detection_methods++))

    # Method 3: Display count check
    local display_count=$(system_profiler SPDisplaysDataType 2>/dev/null | grep -c "Resolution:" || echo "0")
    if [[ $display_count -gt 1 ]]; then
        ((positive_detections++))
        log_message "DEBUG" "Method 3 (display count): Multiple displays detected ($display_count)"
    fi
    ((detection_methods++))

    # Method 4: Clamshell mode check
    if ioreg -r -k AppleClamshellState -d 4 2>/dev/null | grep -q "AppleClamshellState.*Yes"; then
        ((positive_detections++))
        log_message "DEBUG" "Method 4 (clamshell): Clamshell mode active"
    fi
    ((detection_methods++))

    log_message "DEBUG" "External monitor detection: $positive_detections/$detection_methods methods positive"

    # Require at least 2 positive detections for confidence
    if [[ $positive_detections -ge 2 ]]; then
        return 0
    else
        return 1
    fi
}

# State Management Functions
load_state() {
    # Initialize default state
    CURRENT_STATE='{
        "monitor": "disconnected",
        "charging": "unknown",
        "battery": "normal",
        "last_battery_perc": 50,
        "last_temperature": 40,
        "last_health_check": 0,
        "sailing_mode_active": false,
        "travel_mode_active": false,
        "thermal_protection_active": false,
        "last_webhook_action": "",
        "last_webhook_time": 0
    }'

    # Load existing state if available
    if [[ -f "$STATE_FILE" ]]; then
        CURRENT_STATE=$(cat "$STATE_FILE" 2>/dev/null || echo "$CURRENT_STATE")
    fi
}

save_state() {
    echo "$CURRENT_STATE" >"$STATE_FILE"
}

get_state_value() {
    local key=$1
    echo "$CURRENT_STATE" | grep -o "\"$key\": *\"[^\"]*\"" | cut -d'"' -f4
}

set_state_value() {
    local key=$1
    local value=$2
    CURRENT_STATE=$(echo "$CURRENT_STATE" | sed "s/\"$key\": *\"[^\"]*\"/\"$key\": \"$value\"/")
}

# Advanced Battery Health Management Functions
check_sailing_mode() {
    local battery_perc=$1
    local current_charging_state=$2
    local sailing_enabled=$(grep -o '"sailing_mode_enabled": *true' "$CONFIG_FILE" 2>/dev/null)

    if [[ -z "$sailing_enabled" ]]; then
        return 0 # Sailing mode disabled
    fi

    local sailing_active=$(get_state_value "sailing_mode_active")
    local last_health_check=$(get_state_value "last_health_check")
    local current_time=$(date +%s)

    # Check if it's time for sailing mode (every 7 days)
    local time_since_last=$((current_time - last_health_check))
    local seven_days=$((7 * 24 * 3600))

    if [[ $time_since_last -gt $seven_days ]] && [[ "$battery_perc" -gt $SAILING_THRESHOLD ]]; then
        if [[ "$sailing_active" != "true" ]]; then
            log_message "INFO" "Initiating sailing mode for battery health maintenance"
            send_smart_notification "sailing_mode" "$battery_perc" "Health maintenance cycle"
            set_state_value "sailing_mode_active" "true"
            set_state_value "last_health_check" "$current_time"

            # Stop charging to allow natural discharge
            if [[ "$current_charging_state" == "charging" ]]; then
                trigger_webhook "off" "sailing_mode"
            fi
            return 1 # Sailing mode activated
        fi
    elif [[ "$battery_perc" -le $SAILING_THRESHOLD ]] && [[ "$sailing_active" == "true" ]]; then
        log_message "INFO" "Sailing mode complete - battery discharged to healthy level"
        send_smart_notification "sailing_mode" "$battery_perc" "Health maintenance complete"
        set_state_value "sailing_mode_active" "false"
        return 0 # Sailing mode completed
    fi

    return 0
}

check_travel_mode() {
    local battery_perc=$1
    local travel_active=$(get_state_value "travel_mode_active")

    # Check if travel mode file exists (manual trigger)
    if [[ -f "$CONFIG_DIR/travel_mode" ]]; then
        if [[ "$travel_active" != "true" ]]; then
            log_message "INFO" "Travel mode activated - charging to 100%"
            send_smart_notification "travel_mode" "$battery_perc" "Preparing for travel"
            set_state_value "travel_mode_active" "true"
            return 1 # Travel mode activated
        fi

        # Continue travel mode until battery reaches 100%
        if [[ "$battery_perc" -ge $TRAVEL_THRESHOLD ]]; then
            log_message "INFO" "Travel mode complete - battery fully charged"
            send_smart_notification "travel_mode" "$battery_perc" "Ready for travel"
            rm -f "$CONFIG_DIR/travel_mode"
            set_state_value "travel_mode_active" "false"
            return 0 # Travel mode completed
        fi
        return 1 # Travel mode continues
    else
        if [[ "$travel_active" == "true" ]]; then
            set_state_value "travel_mode_active" "false"
        fi
        return 0 # Travel mode not active
    fi
}

analyze_battery_health() {
    local health_info=$1
    local cycle_count=${health_info#*:}
    local max_capacity=${health_info%:*}

    # Battery health analysis
    local health_status="good"
    local health_message=""

    if [[ $max_capacity -lt 80 ]]; then
        health_status="poor"
        health_message="Battery capacity below 80% ($max_capacity%). Consider replacement."
    elif [[ $max_capacity -lt 90 ]]; then
        health_status="fair"
        health_message="Battery capacity declining ($max_capacity%). Monitor closely."
    fi

    if [[ $cycle_count -gt 1000 ]]; then
        health_status="aged"
        health_message="High cycle count ($cycle_count). Battery may need replacement soon."
    elif [[ $cycle_count -gt 500 ]]; then
        if [[ "$health_status" == "good" ]]; then
            health_status="moderate"
            health_message="Moderate cycle count ($cycle_count). Battery aging normally."
        fi
    fi

    # Send health warning if needed
    if [[ "$health_status" != "good" ]]; then
        send_smart_notification "health_warning" "" "$health_message"
        log_message "WARN" "Battery health warning: $health_message"
    fi

    echo "$health_status"
}

# Enhanced Charging Schedule Management
check_charging_schedule() {
    local battery_perc=$1
    local current_time=$(date +%s)
    local current_hour=$(date +%H)
    local current_day=$(date +%u) # 1=Monday, 7=Sunday

    # Load schedule preferences from config
    local schedule_enabled=$(grep -o '"schedule_enabled": *true' "$CONFIG_FILE" 2>/dev/null)
    if [[ -z "$schedule_enabled" ]]; then
        return 0 # Scheduling disabled
    fi

    # Get schedule configuration
    local night_hours_start=$(grep -o '"night_hours_start": *[0-9]*' "$CONFIG_FILE" 2>/dev/null | grep -o '[0-9]*' || echo "22")
    local night_hours_end=$(grep -o '"night_hours_end": *[0-9]*' "$CONFIG_FILE" 2>/dev/null | grep -o '[0-9]*' || echo "6")
    local work_days_only=$(grep -o '"work_days_only": *true' "$CONFIG_FILE" 2>/dev/null)

    # Check if we're in night hours (optimized charging time)
    local in_night_hours=false
    if [[ $night_hours_start -gt $night_hours_end ]]; then
        # Night hours cross midnight (e.g., 22:00 to 06:00)
        if [[ $current_hour -ge $night_hours_start ]] || [[ $current_hour -lt $night_hours_end ]]; then
            in_night_hours=true
        fi
    else
        # Night hours within same day
        if [[ $current_hour -ge $night_hours_start ]] && [[ $current_hour -lt $night_hours_end ]]; then
            in_night_hours=true
        fi
    fi

    # Check work days restriction
    local is_work_day=true
    if [[ -n "$work_days_only" ]] && [[ $current_day -gt 5 ]]; then
        is_work_day=false # Weekend
    fi

    # Determine schedule-based charging preference
    if [[ "$in_night_hours" == true ]] && [[ "$is_work_day" == true ]]; then
        # Night hours on work days - optimal charging time
        log_message "INFO" "In scheduled charging window (night hours)"
        return 1 # Prefer charging
    elif [[ "$battery_perc" -lt 20 ]]; then
        # Emergency charging regardless of schedule
        log_message "INFO" "Emergency charging override (battery < 20%)"
        return 1 # Force charging
    else
        # Outside optimal charging window
        log_message "DEBUG" "Outside scheduled charging window"
        return 0 # Normal operation
    fi
}

# Usage Pattern Analysis
analyze_usage_patterns() {
    local current_time=$(date +%s)
    local usage_log="$CONFIG_DIR/usage_patterns.log"

    # Log current usage data
    local battery_perc=$(get_battery_percentage)
    local power_source=$(get_power_source)
    local monitor_connected=$(is_external_monitor_connected && echo "true" || echo "false")

    # Append to usage log (keep last 30 days)
    echo "$current_time,$battery_perc,$power_source,$monitor_connected" >>"$usage_log"

    # Clean old entries (older than 30 days)
    local thirty_days_ago=$((current_time - 30 * 24 * 3600))
    if [[ -f "$usage_log" ]]; then
        awk -F',' -v cutoff="$thirty_days_ago" '$1 > cutoff' "$usage_log" >"$usage_log.tmp" && mv "$usage_log.tmp" "$usage_log"
    fi

    # Analyze patterns for intelligent scheduling
    if [[ -f "$usage_log" ]] && [[ $(wc -l <"$usage_log") -gt 50 ]]; then
        # Calculate average daily usage patterns
        local avg_morning_usage=$(awk -F',' -v start="$((current_time - 7 * 24 * 3600))" '
            $1 > start {
                hour = strftime("%H", $1)
                if (hour >= 6 && hour <= 12) sum += $2; count++
            }
            END { if (count > 0) print int(sum/count); else print 50 }
        ' "$usage_log")

        local avg_evening_usage=$(awk -F',' -v start="$((current_time - 7 * 24 * 3600))" '
            $1 > start {
                hour = strftime("%H", $1)
                if (hour >= 18 && hour <= 23) sum += $2; count++
            }
            END { if (count > 0) print int(sum/count); else print 50 }
        ' "$usage_log")

        log_message "DEBUG" "Usage patterns - Morning avg: ${avg_morning_usage}%, Evening avg: ${avg_evening_usage}%"

        # Store patterns for future use
        echo "{\"morning_avg\": $avg_morning_usage, \"evening_avg\": $avg_evening_usage, \"last_updated\": $current_time}" >"$CONFIG_DIR/usage_patterns.json"
    fi
}

# Predictive Charging Management
calculate_predictive_charging() {
    local battery_perc=$1
    local current_time=$(date +%s)
    local patterns_file="$CONFIG_DIR/usage_patterns.json"

    if [[ ! -f "$patterns_file" ]]; then
        return 0 # No patterns available
    fi

    # Load usage patterns
    local morning_avg=$(grep -o '"morning_avg": *[0-9]*' "$patterns_file" | grep -o '[0-9]*' || echo "50")
    local evening_avg=$(grep -o '"evening_avg": *[0-9]*' "$patterns_file" | grep -o '[0-9]*' || echo "50")

    local current_hour=$(date +%H)
    local predicted_need=50

    # Predict charging needs based on time and patterns
    if [[ $current_hour -ge 22 ]] || [[ $current_hour -le 6 ]]; then
        # Night time - prepare for morning usage
        predicted_need=$morning_avg
    elif [[ $current_hour -ge 6 ]] && [[ $current_hour -le 12 ]]; then
        # Morning - prepare for day usage
        predicted_need=$((morning_avg + 20))
    elif [[ $current_hour -ge 12 ]] && [[ $current_hour -le 18 ]]; then
        # Afternoon - prepare for evening usage
        predicted_need=$evening_avg
    else
        # Evening - maintain current level
        predicted_need=$battery_perc
    fi

    # Adjust charging target based on prediction
    local charging_target=$UPPER_THRESHOLD
    if [[ $predicted_need -gt 70 ]]; then
        charging_target=$((UPPER_THRESHOLD + 10)) # Charge higher for heavy usage
    elif [[ $predicted_need -lt 30 ]]; then
        charging_target=$((UPPER_THRESHOLD - 10)) # Charge less for light usage
    fi

    # Ensure target is within safe bounds
    if [[ $charging_target -gt 95 ]]; then
        charging_target=95
    elif [[ $charging_target -lt 60 ]]; then
        charging_target=60
    fi

    log_message "DEBUG" "Predictive charging - Need: ${predicted_need}%, Target: ${charging_target}%"
    echo "$charging_target"
}

# Main Advanced Battery Management Function
manage_battery() {
    log_message "DEBUG" "Starting battery management cycle"

    # Get current battery information with timeout protection
    local battery_perc
    battery_perc=$(timeout 10 get_battery_percentage 2>/dev/null || echo "50")
    log_message "DEBUG" "Retrieved battery percentage: ${battery_perc}%"

    local temperature
    temperature=$(timeout 5 get_battery_temperature 2>/dev/null || echo "40")

    local health_info
    health_info=$(timeout 10 get_battery_health 2>/dev/null || echo "Unknown")

    local charging_state
    charging_state=$(timeout 5 get_charging_state 2>/dev/null || echo "unknown")
    log_message "DEBUG" "Retrieved charging state: $charging_state"

    local power_source
    power_source=$(timeout 5 get_power_source 2>/dev/null || echo "unknown")
    log_message "DEBUG" "Retrieved power source: $power_source"

    # Update state with current values immediately
    set_state_value "last_battery_perc" "$battery_perc"
    set_state_value "battery" "normal"
    if [[ "$charging_state" != "unknown" ]]; then
        set_state_value "charging" "$charging_state"
    fi

    # Log current status
    log_battery_health "$battery_perc" "$temperature" "$health_info" "$charging_state"
    log_message "INFO" "Battery status: ${battery_perc}%, Temp: ${temperature}°C, Source: $power_source"

    # Update state with current values
    set_state_value "last_battery_perc" "$battery_perc"
    set_state_value "last_temperature" "$temperature"

    # Check thermal state first (highest priority)
    local thermal_status
    check_thermal_state "$temperature" "$charging_state"
    thermal_status=$?

    if [[ $thermal_status -eq 2 ]]; then
        # Critical temperature - emergency stop
        set_state_value "thermal_protection_active" "true"
        save_state
        return 1
    elif [[ $thermal_status -eq 1 ]]; then
        # Warning temperature - monitor closely
        set_state_value "thermal_protection_active" "true"
    else
        set_state_value "thermal_protection_active" "false"
    fi

    # Check external monitor connection and manage clamshell mode (with timeout)
    local current_monitor_state=$(get_state_value "monitor")
    local monitor_connected=false

    # Simple external monitor detection with fallback
    # Try quick detection methods with timeouts
    local detection_result=1

    # Method 1: Quick osascript check
    if command -v osascript >/dev/null 2>&1; then
        local screen_count
        screen_count=$(timeout 3 osascript -e 'tell application "System Events" to count desktops' 2>/dev/null || echo "1")
        if [[ "$screen_count" =~ ^[0-9]+$ ]] && [[ $screen_count -gt 1 ]]; then
            monitor_connected=true
            detection_result=0
            log_message "DEBUG" "External monitor detected via AppleScript ($screen_count desktops)"
        fi
    fi

    # Method 2: Check clamshell mode if first method didn't detect
    if [[ $detection_result -ne 0 ]] && command -v ioreg >/dev/null 2>&1; then
        if timeout 3 ioreg -r -k AppleClamshellState -d 4 2>/dev/null | grep -q "AppleClamshellState.*Yes"; then
            monitor_connected=true
            detection_result=0
            log_message "DEBUG" "External monitor detected via clamshell mode"
        fi
    fi

    # If no detection, assume disconnected
    if [[ $detection_result -ne 0 ]]; then
        log_message "DEBUG" "No external monitor detected"
    fi

    if [[ "$monitor_connected" == true ]]; then
        if [[ "$current_monitor_state" != "connected" ]]; then
            manage_sleep_protection "true" "external_monitor"
            send_smart_notification "monitor_connected" "$battery_perc" "Clamshell mode active"
            log_message "INFO" "External monitor connected. Clamshell mode enabled."
            set_state_value "monitor" "connected"
        fi
    else
        if [[ "$current_monitor_state" != "disconnected" ]]; then
            manage_sleep_protection "false" "monitor_disconnected"
            send_smart_notification "monitor_disconnected" "$battery_perc" "Normal power management"
            log_message "INFO" "External monitor disconnected. Normal power management restored."
            set_state_value "monitor" "disconnected"
        fi
    fi

    # Check for special modes (travel mode has priority over sailing mode)
    local travel_mode_result
    check_travel_mode "$battery_perc"
    travel_mode_result=$?

    local sailing_mode_result=0
    if [[ $travel_mode_result -eq 0 ]]; then
        # Only check sailing mode if travel mode is not active
        check_sailing_mode "$battery_perc" "$charging_state"
        sailing_mode_result=$?
    fi

    # Analyze usage patterns for intelligent scheduling
    analyze_usage_patterns

    # Check charging schedule preferences
    local schedule_result
    check_charging_schedule "$battery_perc"
    schedule_result=$?

    # Calculate predictive charging target
    local predictive_target=$(calculate_predictive_charging "$battery_perc")

    # Determine charging thresholds based on active modes and scheduling
    local effective_lower_threshold=$LOWER_THRESHOLD
    local effective_upper_threshold=$UPPER_THRESHOLD
    local charging_priority="normal"

    if [[ $travel_mode_result -eq 1 ]]; then
        # Travel mode: charge to 100% (highest priority)
        effective_upper_threshold=$TRAVEL_THRESHOLD
        charging_priority="travel"
        log_message "INFO" "Travel mode active - using travel threshold: $effective_upper_threshold%"
    elif [[ $sailing_mode_result -eq 1 ]]; then
        # Sailing mode: prevent charging to allow discharge
        effective_upper_threshold=0 # Prevent charging
        charging_priority="sailing"
        log_message "INFO" "Sailing mode active - charging disabled for health maintenance"
    elif [[ $schedule_result -eq 1 ]]; then
        # Scheduled charging window - use predictive target
        if [[ $predictive_target -gt 0 ]]; then
            effective_upper_threshold=$predictive_target
            charging_priority="scheduled"
            log_message "INFO" "Scheduled charging active - using predictive target: $effective_upper_threshold%"
        fi
    elif [[ $predictive_target -gt 0 ]] && [[ $predictive_target -ne $UPPER_THRESHOLD ]]; then
        # Use predictive charging outside scheduled hours
        effective_upper_threshold=$predictive_target
        charging_priority="predictive"
        log_message "INFO" "Predictive charging - adjusted target: $effective_upper_threshold%"
    fi

    # Simplified charging management - bypass complex state system
    local current_charging_state="$charging_state" # Use actual detected state, not stored state
    local should_charge=false
    local should_stop_charge=false
    local charge_reason=""

    log_message "DEBUG" "Using actual charging state: $current_charging_state (not stored state)"

    # Debug logging for charging decision
    log_message "DEBUG" "Charging decision - Battery: ${battery_perc}%, Power: $power_source, Current state: $current_charging_state"
    log_message "DEBUG" "Thresholds - Lower: $effective_lower_threshold%, Upper: $effective_upper_threshold%"
    log_message "DEBUG" "Thermal status: $thermal_status"

    # Simplified charging decision logic
    if [[ "$power_source" == "ac" ]] || [[ "$power_source" == "unknown" ]]; then
        log_message "DEBUG" "On AC power (or unknown) - evaluating charging logic"

        # Force charging if battery is low, regardless of current state
        if [[ "$battery_perc" -le $effective_lower_threshold ]]; then
            log_message "INFO" "Battery ${battery_perc}% <= threshold ${effective_lower_threshold}% - FORCING CHARGE START"
            # Always try to start charging when battery is low
            if [[ "$current_charging_state" != "charging" ]] && [[ $thermal_status -lt 2 ]]; then
                should_charge=true
                charge_reason="battery_low"
                log_message "INFO" "🔥 CHARGING NEEDED: Battery low, starting charge"
            else
                log_message "DEBUG" "Already charging or thermal protection active"
            fi
        elif [[ "$battery_perc" -ge $effective_upper_threshold ]]; then
            log_message "DEBUG" "Battery ${battery_perc}% >= threshold ${effective_upper_threshold}% - should stop"
            # Stop charging if currently charging
            if [[ "$current_charging_state" == "charging" ]]; then
                should_stop_charge=true
                charge_reason="battery_high"
                log_message "DEBUG" "Setting should_stop_charge=true"
            fi
        else
            log_message "DEBUG" "Battery in normal range (${effective_lower_threshold}%-${effective_upper_threshold}%) - no action needed"
        fi

        # Override for thermal protection
        if [[ $thermal_status -ge 1 ]] && [[ "$current_charging_state" == "charging" ]]; then
            should_stop_charge=true
            should_charge=false
            charge_reason="thermal_protection"
            log_message "DEBUG" "Thermal protection override - stopping charge"
        fi
    else
        log_message "DEBUG" "On battery power - cannot manage charging"
    fi

    # Execute charging actions with immediate state updates
    if [[ "$should_charge" == true ]]; then
        local full_reason="${charge_reason} (${charging_priority} priority)"
        log_message "INFO" "🔥 EXECUTING: Starting charge - Battery: ${battery_perc}%, Reason: $full_reason"

        if trigger_webhook "on" "$full_reason"; then
            log_message "INFO" "✅ Webhook SUCCESS: Charging started at ${battery_perc}%"
            # Update state immediately
            set_state_value "charging" "charging"
            set_state_value "last_webhook_action" "on"
            set_state_value "last_webhook_time" "$(date +%s)"
            set_state_value "charging_priority" "$charging_priority"
            send_smart_notification "charging_started" "$battery_perc" "Priority: $charging_priority, Target: $effective_upper_threshold%"
        else
            log_message "ERROR" "❌ Webhook FAILED: Could not start charging"
            set_state_value "charging" "webhook_failed"
        fi
    elif [[ "$should_stop_charge" == true ]]; then
        local full_reason="${charge_reason} (${charging_priority} priority)"
        log_message "INFO" "🛑 EXECUTING: Stopping charge - Battery: ${battery_perc}%, Reason: $full_reason"

        if trigger_webhook "off" "$full_reason"; then
            log_message "INFO" "✅ Webhook SUCCESS: Charging stopped at ${battery_perc}%"
            # Update state immediately
            set_state_value "charging" "not_charging"
            set_state_value "last_webhook_action" "off"
            set_state_value "last_webhook_time" "$(date +%s)"
            set_state_value "charging_priority" "$charging_priority"
            send_smart_notification "charging_stopped" "$battery_perc" "Priority: $charging_priority, Reason: $charge_reason"
        else
            log_message "ERROR" "❌ Webhook FAILED: Could not stop charging"
            set_state_value "charging" "webhook_failed"
        fi
    else
        log_message "DEBUG" "No charging action needed - Battery: ${battery_perc}%, State: $current_charging_state"
    fi

    # Analyze battery health periodically
    local health_status=$(analyze_battery_health "$health_info")
    log_message "INFO" "Battery health status: $health_status"

    # Save current state
    save_state

    log_message "INFO" "Battery management cycle completed successfully"
}

# Utility functions for manual control
enable_travel_mode() {
    touch "$CONFIG_DIR/travel_mode"
    log_message "INFO" "Travel mode enabled manually"
    send_smart_notification "travel_mode" "$(get_battery_percentage)" "Manually activated"
}

disable_travel_mode() {
    rm -f "$CONFIG_DIR/travel_mode"
    log_message "INFO" "Travel mode disabled manually"
    send_smart_notification "travel_mode" "$(get_battery_percentage)" "Manually deactivated"
}

# Schedule management functions
enable_schedule() {
    local config_content=$(cat "$CONFIG_FILE" 2>/dev/null || echo '{}')
    # Simple JSON update - in production, use proper JSON parser
    echo "$config_content" | sed 's/"schedule_enabled": *false/"schedule_enabled": true/' >"$CONFIG_FILE.tmp"
    if ! grep -q '"schedule_enabled"' "$CONFIG_FILE.tmp"; then
        echo "$config_content" | sed 's/}$/, "schedule_enabled": true}/' >"$CONFIG_FILE.tmp"
    fi
    mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"
    log_message "INFO" "Charging schedule enabled"
    echo "Charging schedule enabled. Default hours: 22:00-06:00 on work days."
}

disable_schedule() {
    local config_content=$(cat "$CONFIG_FILE" 2>/dev/null || echo '{}')
    echo "$config_content" | sed 's/"schedule_enabled": *true/"schedule_enabled": false/' >"$CONFIG_FILE.tmp"
    mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"
    log_message "INFO" "Charging schedule disabled"
    echo "Charging schedule disabled."
}

# Reset usage patterns
reset_patterns() {
    rm -f "$CONFIG_DIR/usage_patterns.log"
    rm -f "$CONFIG_DIR/usage_patterns.json"
    log_message "INFO" "Usage patterns reset"
    echo "Usage patterns have been reset. New patterns will be learned over time."
}

# Force sailing mode
force_sailing_mode() {
    set_state_value "sailing_mode_active" "true"
    set_state_value "last_health_check" "0" # Force immediate activation
    save_state
    log_message "INFO" "Sailing mode forced manually"
    send_smart_notification "sailing_mode" "$(get_battery_percentage)" "Manually activated"
    echo "Sailing mode activated. Battery will discharge for health maintenance."
}

show_status() {
    load_config
    load_state

    local battery_perc=$(get_battery_percentage)
    local temperature=$(get_battery_temperature)
    local health_info=$(get_battery_health)
    local charging_state=$(get_charging_state)
    local power_source=$(get_power_source)

    # Get scheduling information
    local schedule_result
    check_charging_schedule "$battery_perc"
    schedule_result=$?
    local schedule_status="Disabled"
    if [[ $schedule_result -eq 1 ]]; then
        schedule_status="Active (optimal charging window)"
    elif grep -q '"schedule_enabled": *true' "$CONFIG_FILE" 2>/dev/null; then
        schedule_status="Enabled (outside window)"
    fi

    # Get predictive charging target
    local predictive_target=$(calculate_predictive_charging "$battery_perc")

    # Get usage patterns if available
    local patterns_info="No data"
    if [[ -f "$CONFIG_DIR/usage_patterns.json" ]]; then
        local morning_avg=$(grep -o '"morning_avg": *[0-9]*' "$CONFIG_DIR/usage_patterns.json" | grep -o '[0-9]*' || echo "N/A")
        local evening_avg=$(grep -o '"evening_avg": *[0-9]*' "$CONFIG_DIR/usage_patterns.json" | grep -o '[0-9]*' || echo "N/A")
        patterns_info="Morning: ${morning_avg}%, Evening: ${evening_avg}%"
    fi

    echo "=== Advanced Battery Health Manager Status ==="
    echo "🔋 Battery Level: ${battery_perc}%"
    echo "🌡️  Temperature: ${temperature}°C"
    echo "💡 Health Info: $health_info"
    echo "⚡ Charging State: $charging_state"
    echo "🔌 Power Source: $power_source"
    echo "🖥️  Monitor State: $(get_state_value "monitor")"
    echo ""
    echo "--- Advanced Features ---"
    echo "✈️  Travel Mode: $(get_state_value "travel_mode_active")"
    echo "⛵ Sailing Mode: $(get_state_value "sailing_mode_active")"
    echo "🔥 Thermal Protection: $(get_state_value "thermal_protection_active")"
    echo "📅 Charging Schedule: $schedule_status"
    echo "🎯 Predictive Target: ${predictive_target}%"
    echo "📊 Usage Patterns: $patterns_info"
    echo "🏷️  Last Priority: $(get_state_value "charging_priority")"
    echo ""
    echo "--- Configuration ---"
    echo "📁 Config: $CONFIG_FILE"
    echo "💾 State: $STATE_FILE"
    echo "📝 Log: $LOG_FILE"
    echo "=============================================="
}

# Main execution
# Enable error trapping
set_error_trap

case "${1:-manage}" in
"manage")
    # Simplified initialization - bypass complex profile system
    log_message "INFO" "🔋 Starting Battery Management (Simplified Mode)"

    # Load basic config with defaults
    LOWER_THRESHOLD=${LOWER_THRESHOLD:-$DEFAULT_LOWER_THRESHOLD}
    UPPER_THRESHOLD=${UPPER_THRESHOLD:-$DEFAULT_UPPER_THRESHOLD}
    TRAVEL_THRESHOLD=${TRAVEL_THRESHOLD:-$DEFAULT_TRAVEL_THRESHOLD}
    SAILING_THRESHOLD=${SAILING_THRESHOLD:-$DEFAULT_SAILING_THRESHOLD}
    TEMP_WARNING=${TEMP_WARNING:-$DEFAULT_TEMP_WARNING}
    TEMP_CRITICAL=${TEMP_CRITICAL:-$DEFAULT_TEMP_CRITICAL}

    log_message "INFO" "Using thresholds - Lower: ${LOWER_THRESHOLD}%, Upper: ${UPPER_THRESHOLD}%"

    # Initialize basic state if needed
    if [[ ! -f "$STATE_FILE" ]]; then
        create_default_state
    fi

    # Load minimal state
    load_state

    # Run battery management
    manage_battery
    ;;
"status")
    # Load config for status display
    load_config
    load_state
    show_status
    ;;
"travel-on")
    enable_travel_mode
    ;;
"travel-off")
    disable_travel_mode
    ;;
"sailing-on")
    force_sailing_mode
    ;;
"schedule-on")
    enable_schedule
    ;;
"schedule-off")
    disable_schedule
    ;;
"reset-patterns")
    reset_patterns
    ;;
"config")
    echo "Configuration file: $CONFIG_FILE"
    echo "State file: $STATE_FILE"
    echo "Log file: $LOG_FILE"
    echo "Usage patterns: $CONFIG_DIR/usage_patterns.log"
    ;;
"help" | "-h" | "--help")
    echo "Advanced Battery Health Manager for macOS"
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "COMMANDS:"
    echo "  manage         - Run battery management (default)"
    echo "  status         - Show detailed status and configuration"
    echo ""
    echo "MODE CONTROLS:"
    echo "  travel-on      - Enable travel mode (charge to 100%)"
    echo "  travel-off     - Disable travel mode"
    echo "  sailing-on     - Force sailing mode (health maintenance)"
    echo ""
    echo "SCHEDULE CONTROLS:"
    echo "  schedule-on    - Enable intelligent charging schedule"
    echo "  schedule-off   - Disable charging schedule"
    echo "  reset-patterns - Reset learned usage patterns"
    echo ""
    echo "PROFILE MANAGEMENT:"
    echo "  profile-list   - List available profiles"
    echo "  profile-load   - Load a specific profile"
    echo "  profile-create - Create a new profile"
    echo "  profile-delete - Delete a profile"
    echo ""
    echo "INFORMATION:"
    echo "  config         - Show configuration file locations"
    echo "  help           - Show this help message"
    echo ""
    echo "FEATURES:"
    echo "  • Intelligent charging limits with thermal protection"
    echo "  • External monitor detection and clamshell mode"
    echo "  • Usage pattern learning and predictive charging"
    echo "  • Sailing mode for battery health maintenance"
    echo "  • Travel mode for full charging when needed"
    echo "  • Smart notifications and comprehensive logging"
    echo "  • Profile-based configuration management"
    echo "  • Robust error handling and fail-safes"
    ;;
"profile-list")
    list_profiles
    ;;
"profile-load")
    if [[ -z "$2" ]]; then
        echo "Error: Profile name required"
        echo "Usage: $0 profile-load <profile_name>"
        exit 1
    fi
    load_profile "$2"
    ;;
"profile-create")
    if [[ -z "$2" ]]; then
        echo "Error: Profile name required"
        echo "Usage: $0 profile-create <profile_name> [description]"
        exit 1
    fi
    create_profile "$2" "$3"
    ;;
"profile-delete")
    if [[ -z "$2" ]]; then
        echo "Error: Profile name required"
        echo "Usage: $0 profile-delete <profile_name>"
        exit 1
    fi
    delete_profile "$2"
    ;;
*)
    echo "Unknown command: $1"
    echo "Use '$0 help' for usage information."
    exit 1
    ;;
esac
