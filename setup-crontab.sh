#!/bin/bash

echo "🔧 Setting up Battery Management Crontab"
echo "========================================"

# Remove any existing battery-limit entries
echo "Removing existing battery-limit crontab entries..."
crontab -l 2>/dev/null | grep -v "battery-limit.sh" | crontab -

# Add the correct crontab entry
echo "Adding new crontab entry..."
(
    crontab -l 2>/dev/null
    echo "* * * * * /usr/bin/bash /Users/<USER>/.scripts/battery-limit.sh manage"
) | crontab -

echo ""
echo "✅ Crontab updated. Current crontab entries:"
crontab -l

echo ""
echo "🧪 Testing the corrected script manually first..."
echo "================================================="

cd /Users/<USER>/.scripts

# Test the script manually
echo "Running: ./battery-limit.sh manage"
./battery-limit.sh manage

echo ""
echo "📋 Checking logs for correct behavior:"
if [[ -f "$HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "Recent log entries:"
    tail -20 "$HOME/.battery_health_manager/battery_health.log"
else
    echo "❌ No log file found"
fi

echo ""
echo "🔍 Key things to verify in the logs:"
echo "1. Battery percentage detected correctly (should be around 50%)"
echo "2. Thresholds shown correctly (Lower: 70%, Upper: 90% or 80% if sailing mode)"
echo "3. Logic evaluation: 50% <= 70% should be TRUE"
echo "4. Action decided: Should be START charging (not STOP)"
echo "5. Webhook sent: Should be 'on' webhook (not 'off')"

echo ""
echo "📊 Current battery status:"
pmset -g batt
