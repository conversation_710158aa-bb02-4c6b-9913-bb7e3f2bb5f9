#!/bin/bash

echo "🔋 Charging Logic Debug"
echo "======================"

# Test battery percentage
echo "1. Battery Percentage:"
battery_info=$(pmset -g batt 2>/dev/null)
echo "Raw pmset output: $battery_info"

if [[ "$battery_info" =~ ([0-9]+)% ]]; then
    battery_perc="${BASH_REMATCH[1]}"
    echo "✅ Battery percentage: ${battery_perc}%"
else
    echo "❌ Could not parse battery percentage"
    battery_perc=50
fi

# Test charging state
echo ""
echo "2. Charging State:"
echo "Checking for 'AC Power': $(echo "$battery_info" | grep -c "AC Power")"
echo "Checking for 'Battery Power': $(echo "$battery_info" | grep -c "Battery Power")"
echo "Checking for 'charging': $(echo "$battery_info" | grep -c "charging")"
echo "Checking for 'discharging': $(echo "$battery_info" | grep -c "discharging")"
echo "Checking for 'charged': $(echo "$battery_info" | grep -c "charged")"

if echo "$battery_info" | grep -q "AC Power"; then
    echo "✅ On AC Power"
    power_source="ac"
    if echo "$battery_info" | grep -q "charging"; then
        charging_state="charging"
        echo "✅ Currently charging"
    elif echo "$battery_info" | grep -q "charged"; then
        charging_state="charged"
        echo "✅ Fully charged"
    else
        charging_state="plugged_not_charging"
        echo "⚡ Plugged in but not charging"
    fi
elif echo "$battery_info" | grep -q "Battery Power"; then
    echo "🔋 On Battery Power"
    power_source="battery"
    charging_state="discharging"
else
    echo "❓ Unknown power state"
    power_source="unknown"
    charging_state="unknown"
fi

# Test thresholds
echo ""
echo "3. Charging Logic:"
LOWER_THRESHOLD=70
UPPER_THRESHOLD=90
echo "Lower threshold: ${LOWER_THRESHOLD}%"
echo "Upper threshold: ${UPPER_THRESHOLD}%"
echo "Current battery: ${battery_perc}%"
echo "Power source: $power_source"
echo "Charging state: $charging_state"

# Determine what should happen
echo ""
echo "4. Decision Logic:"
if [[ "$power_source" == "ac" ]]; then
    echo "✅ On AC power - can manage charging"
    
    if [[ "$battery_perc" -le $LOWER_THRESHOLD ]]; then
        echo "✅ Battery (${battery_perc}%) <= Lower threshold (${LOWER_THRESHOLD}%) - SHOULD START CHARGING"
        if [[ "$charging_state" != "charging" ]]; then
            echo "🔥 ACTION NEEDED: Start charging (current state: $charging_state)"
        else
            echo "✅ Already charging"
        fi
    elif [[ "$battery_perc" -ge $UPPER_THRESHOLD ]]; then
        echo "✅ Battery (${battery_perc}%) >= Upper threshold (${UPPER_THRESHOLD}%) - SHOULD STOP CHARGING"
        if [[ "$charging_state" == "charging" ]]; then
            echo "🔥 ACTION NEEDED: Stop charging"
        else
            echo "✅ Not charging (correct)"
        fi
    else
        echo "ℹ️  Battery in normal range (${LOWER_THRESHOLD}%-${UPPER_THRESHOLD}%) - no action needed"
    fi
else
    echo "❌ Not on AC power - cannot manage charging"
fi

# Test webhook
echo ""
echo "5. Webhook Test:"
WEBHOOK_KEY="990B939B49"
if [[ -n "$WEBHOOK_KEY" && "$WEBHOOK_KEY" != "YOUR_KEY" ]]; then
    echo "✅ Webhook key configured: $WEBHOOK_KEY"
    echo "ON URL: https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66748/switch_on"
    echo "OFF URL: https://sequematic.com/trigger-ifttt-webhook/$WEBHOOK_KEY/66750/switch_off"
else
    echo "❌ Webhook key not configured"
fi
