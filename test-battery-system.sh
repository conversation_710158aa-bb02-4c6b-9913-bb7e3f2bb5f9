#!/bin/bash
#===========================================
# Battery Health Management System Test Suite
# Comprehensive testing for all features
#===========================================

# Test configuration
SCRIPT_PATH="./battery-limit.sh"
TEST_LOG="test-results.log"
PASSED_TESTS=0
FAILED_TESTS=0
TOTAL_TESTS=0

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test helper functions
log_test() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$TEST_LOG"
    echo -e "${BLUE}[TEST]${NC} $1"
}

test_passed() {
    ((PASSED_TESTS++))
    ((TOTAL_TESTS++))
    echo -e "${GREEN}[PASS]${NC} $1"
    echo "PASS: $1" >> "$TEST_LOG"
}

test_failed() {
    ((FAILED_TESTS++))
    ((TOTAL_TESTS++))
    echo -e "${RED}[FAIL]${NC} $1"
    echo "FAIL: $1" >> "$TEST_LOG"
}

test_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    echo "WARN: $1" >> "$TEST_LOG"
}

# Initialize test environment
setup_test_environment() {
    log_test "Setting up test environment"
    
    # Create test log
    echo "Battery Health Management System Test Results" > "$TEST_LOG"
    echo "Test started at: $(date)" >> "$TEST_LOG"
    echo "=============================================" >> "$TEST_LOG"
    
    # Check if script exists
    if [[ ! -f "$SCRIPT_PATH" ]]; then
        test_failed "Script not found: $SCRIPT_PATH"
        exit 1
    fi
    
    # Check script permissions
    if [[ ! -x "$SCRIPT_PATH" ]]; then
        chmod +x "$SCRIPT_PATH"
        test_warning "Made script executable"
    fi
    
    test_passed "Test environment setup complete"
}

# Test 1: Basic script functionality
test_basic_functionality() {
    log_test "Testing basic script functionality"
    
    # Test help command
    if bash "$SCRIPT_PATH" help >/dev/null 2>&1; then
        test_passed "Help command works"
    else
        test_failed "Help command failed"
    fi
    
    # Test status command
    if timeout 10 bash "$SCRIPT_PATH" status >/dev/null 2>&1; then
        test_passed "Status command works"
    else
        test_failed "Status command failed or timed out"
    fi
    
    # Test config command
    if bash "$SCRIPT_PATH" config >/dev/null 2>&1; then
        test_passed "Config command works"
    else
        test_failed "Config command failed"
    fi
}

# Test 2: Configuration management
test_configuration_management() {
    log_test "Testing configuration management"
    
    # Test profile listing
    if bash "$SCRIPT_PATH" profile-list >/dev/null 2>&1; then
        test_passed "Profile listing works"
    else
        test_failed "Profile listing failed"
    fi
    
    # Test profile creation
    if bash "$SCRIPT_PATH" profile-create "test-profile" "Test profile for automated testing" >/dev/null 2>&1; then
        test_passed "Profile creation works"
        
        # Test profile loading
        if bash "$SCRIPT_PATH" profile-load "test-profile" >/dev/null 2>&1; then
            test_passed "Profile loading works"
        else
            test_failed "Profile loading failed"
        fi
        
        # Test profile deletion
        if bash "$SCRIPT_PATH" profile-delete "test-profile" >/dev/null 2>&1; then
            test_passed "Profile deletion works"
        else
            test_failed "Profile deletion failed"
        fi
    else
        test_failed "Profile creation failed"
    fi
}

# Test 3: External monitor detection
test_external_monitor_detection() {
    log_test "Testing external monitor detection"
    
    # Test the detection function by running status
    local status_output
    status_output=$(timeout 10 bash "$SCRIPT_PATH" status 2>&1)
    
    if [[ $? -eq 0 ]]; then
        if echo "$status_output" | grep -q "Monitor State:"; then
            test_passed "External monitor detection integrated"
        else
            test_warning "Monitor state not shown in status (may be normal)"
        fi
    else
        test_failed "Status command failed during monitor detection test"
    fi
}

# Test 4: Battery information retrieval
test_battery_information() {
    log_test "Testing battery information retrieval"
    
    # Test if we can get battery percentage
    local status_output
    status_output=$(timeout 10 bash "$SCRIPT_PATH" status 2>&1)
    
    if [[ $? -eq 0 ]]; then
        if echo "$status_output" | grep -q "Battery Level:"; then
            test_passed "Battery percentage retrieval works"
        else
            test_failed "Battery percentage not shown in status"
        fi
        
        if echo "$status_output" | grep -q "Temperature:"; then
            test_passed "Battery temperature retrieval works"
        else
            test_warning "Battery temperature not shown (may be normal on some systems)"
        fi
        
        if echo "$status_output" | grep -q "Health Info:"; then
            test_passed "Battery health information works"
        else
            test_warning "Battery health info not shown (may be normal)"
        fi
    else
        test_failed "Status command failed during battery info test"
    fi
}

# Test 5: Mode switching
test_mode_switching() {
    log_test "Testing mode switching functionality"
    
    # Test travel mode
    if timeout 10 bash "$SCRIPT_PATH" travel-on >/dev/null 2>&1; then
        test_passed "Travel mode activation works"
        
        if timeout 10 bash "$SCRIPT_PATH" travel-off >/dev/null 2>&1; then
            test_passed "Travel mode deactivation works"
        else
            test_failed "Travel mode deactivation failed"
        fi
    else
        test_failed "Travel mode activation failed"
    fi
    
    # Test sailing mode
    if timeout 10 bash "$SCRIPT_PATH" sailing-on >/dev/null 2>&1; then
        test_passed "Sailing mode activation works"
        
        if timeout 10 bash "$SCRIPT_PATH" sailing-off >/dev/null 2>&1; then
            test_passed "Sailing mode deactivation works"
        else
            test_failed "Sailing mode deactivation failed"
        fi
    else
        test_failed "Sailing mode activation failed"
    fi
}

# Test 6: Scheduling functionality
test_scheduling() {
    log_test "Testing scheduling functionality"
    
    # Test schedule enable/disable
    if timeout 10 bash "$SCRIPT_PATH" schedule-on >/dev/null 2>&1; then
        test_passed "Schedule activation works"
        
        if timeout 10 bash "$SCRIPT_PATH" schedule-off >/dev/null 2>&1; then
            test_passed "Schedule deactivation works"
        else
            test_failed "Schedule deactivation failed"
        fi
    else
        test_failed "Schedule activation failed"
    fi
    
    # Test pattern reset
    if timeout 10 bash "$SCRIPT_PATH" reset-patterns >/dev/null 2>&1; then
        test_passed "Pattern reset works"
    else
        test_failed "Pattern reset failed"
    fi
}

# Test 7: Error handling
test_error_handling() {
    log_test "Testing error handling"
    
    # Test invalid command
    if bash "$SCRIPT_PATH" invalid-command >/dev/null 2>&1; then
        test_failed "Invalid command should fail"
    else
        test_passed "Invalid command properly rejected"
    fi
    
    # Test missing parameters
    if bash "$SCRIPT_PATH" profile-load >/dev/null 2>&1; then
        test_failed "Missing profile name should fail"
    else
        test_passed "Missing parameters properly handled"
    fi
}

# Test 8: File permissions and locations
test_file_system() {
    log_test "Testing file system setup"
    
    # Check if config directory is created
    local config_dir="$HOME/.config/battery-health"
    if [[ -d "$config_dir" ]]; then
        test_passed "Config directory exists"
    else
        test_warning "Config directory not found (will be created on first run)"
    fi
    
    # Test script syntax
    if bash -n "$SCRIPT_PATH"; then
        test_passed "Script syntax is valid"
    else
        test_failed "Script has syntax errors"
    fi
}

# Main test execution
main() {
    echo -e "${BLUE}Battery Health Management System Test Suite${NC}"
    echo "=============================================="
    
    setup_test_environment
    
    echo -e "\n${YELLOW}Running comprehensive tests...${NC}\n"
    
    test_basic_functionality
    test_configuration_management
    test_external_monitor_detection
    test_battery_information
    test_mode_switching
    test_scheduling
    test_error_handling
    test_file_system
    
    # Test summary
    echo -e "\n${BLUE}Test Results Summary${NC}"
    echo "===================="
    echo -e "Total Tests: ${TOTAL_TESTS}"
    echo -e "${GREEN}Passed: ${PASSED_TESTS}${NC}"
    echo -e "${RED}Failed: ${FAILED_TESTS}${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 All tests passed! The battery management system is ready for use.${NC}"
        echo "Test results saved to: $TEST_LOG"
        exit 0
    else
        echo -e "\n${RED}❌ Some tests failed. Please review the issues above.${NC}"
        echo "Test results saved to: $TEST_LOG"
        exit 1
    fi
}

# Run tests
main "$@"
