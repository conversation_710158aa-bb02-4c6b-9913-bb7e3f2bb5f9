#!/bin/bash

echo "🚀 Setting up Battery Management with launchd (macOS recommended)"
echo "================================================================="

# Get absolute paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPT_PATH="$SCRIPT_DIR/battery-limit.sh"
USER_HOME="$(eval echo ~$USER)"
PLIST_DIR="$USER_HOME/Library/LaunchAgents"
PLIST_PATH="$PLIST_DIR/com.battery.health.plist"

echo "Script path: $SCRIPT_PATH"
echo "Plist path: $PLIST_PATH"

# Verify script exists
if [[ ! -f "$SCRIPT_PATH" ]]; then
    echo "❌ Error: Script not found at $SCRIPT_PATH"
    exit 1
fi

# Make script executable
chmod +x "$SCRIPT_PATH"
echo "✅ Script permissions set"

# Create directories
mkdir -p "$PLIST_DIR"
mkdir -p "$USER_HOME/.battery_health_manager"
echo "✅ Directories created"

# Unload existing service if it exists
if launchctl list | grep -q "com.battery.health"; then
    echo "Unloading existing service..."
    launchctl unload "$PLIST_PATH" 2>/dev/null || true
fi

# Create the launchd plist file
echo "Creating launchd plist file..."
cat > "$PLIST_PATH" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.battery.health</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/bin/bash</string>
        <string>$SCRIPT_PATH</string>
        <string>manage</string>
        <string>--debug-env</string>
    </array>
    <key>StartInterval</key>
    <integer>60</integer>
    <key>StandardErrorPath</key>
    <string>$USER_HOME/.battery_health_manager/launchd_errors.log</string>
    <key>StandardOutPath</key>
    <string>$USER_HOME/.battery_health_manager/launchd_output.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
        <key>HOME</key>
        <string>$USER_HOME</string>
        <key>CRON_EXECUTION</key>
        <string>1</string>
    </dict>
    <key>RunAtLoad</key>
    <true/>
</dict>
</plist>
EOF

echo "✅ Plist file created"

# Load the service
echo "Loading launchd service..."
if launchctl load "$PLIST_PATH"; then
    echo "✅ Service loaded successfully"
else
    echo "❌ Failed to load service"
    exit 1
fi

# Start the service
echo "Starting service..."
if launchctl start com.battery.health; then
    echo "✅ Service started successfully"
else
    echo "⚠️  Service may have started automatically (RunAtLoad=true)"
fi

echo ""
echo "🧪 Testing the service:"
echo "======================="

# Wait a moment for the service to run
echo "Waiting 10 seconds for service to execute..."
sleep 10

# Check if logs were created
if [[ -f "$USER_HOME/.battery_health_manager/battery_health.log" ]]; then
    echo "✅ Battery health log created"
    echo "Recent entries:"
    tail -10 "$USER_HOME/.battery_health_manager/battery_health.log"
else
    echo "❌ No battery health log found"
fi

if [[ -f "$USER_HOME/.battery_health_manager/launchd_output.log" ]]; then
    echo ""
    echo "✅ Launchd output log:"
    cat "$USER_HOME/.battery_health_manager/launchd_output.log"
fi

if [[ -f "$USER_HOME/.battery_health_manager/launchd_errors.log" ]]; then
    echo ""
    echo "⚠️  Launchd errors:"
    cat "$USER_HOME/.battery_health_manager/launchd_errors.log"
fi

echo ""
echo "📋 Service Management Commands:"
echo "=============================="
echo "Check status:    launchctl list | grep battery"
echo "Stop service:    launchctl stop com.battery.health"
echo "Start service:   launchctl start com.battery.health"
echo "Unload service:  launchctl unload $PLIST_PATH"
echo "Reload service:  launchctl unload $PLIST_PATH && launchctl load $PLIST_PATH"

echo ""
echo "📊 Log Monitoring:"
echo "=================="
echo "Main log:        tail -f $USER_HOME/.battery_health_manager/battery_health.log"
echo "Launchd output:  tail -f $USER_HOME/.battery_health_manager/launchd_output.log"
echo "Launchd errors:  tail -f $USER_HOME/.battery_health_manager/launchd_errors.log"

echo ""
echo "🎯 Current battery status:"
echo "=========================="
pmset -g batt

echo ""
echo "✅ Setup complete! The service will run every 60 seconds."
echo "   Monitor the logs to verify it's working correctly."
